<template>
  <div id="callphone">
    <div class="call-page">
      <span>您确定拨打：</span>
      <span id="Calphone">{{ displayPhone }}</span>
    </div>
  </div>
  <div id="output" class="output" v-show="showOutput">
    <div v-for="(message, index) in outputMessages" :key="index">
      <p v-html="message" style="word-wrap: break-word;"></p>
    </div>
  </div>
  <div class="callBtn">
    <img class="chuzhiimg" @click="callout" src="@/assets/images/phone.png" alt="拨打">
    <img class="chuzhiimg" @click="wsexit" src="@/assets/images/dialognewclose.png" alt="关闭">
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { defineProps } from 'vue'

// 新增：定义props
const props = defineProps({
  phoneNumber: {
    type: String,
    default: ''
  }
})

// 响应式数据
const phoneNum = ref('')
const callPhoneNo = ref('')
const CallWorkNo = ref('')
const displayPhone = ref('')
const outputMessages = ref([])
const showOutput = ref(false)
const socket = ref(null)

// 工具函数
const getQueryString = (name) => {
  const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i")
  const r = window.location.search.substring(1).match(reg)
  let context = ""
  if (r != null) {
    context = decodeURIComponent(r[2])
  }
  return context == null || context == "" || context == "undefined" ? "" : context
}

const hidePhone = (str) => {
  const hiddenStr = str.substring(0, 4) + str.substring(4).replace(/./g, '*')
  return hiddenStr
}

const getCookie = (name) => {
  let cookieValue = null
  if (document.cookie && document.cookie != '') {
    const cookies = document.cookie.split(';')
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].replace(/(^\s*)|(\s*$)/g, '')
      if (cookie.substring(0, name.length + 1) == (name + '=')) {
        cookieValue = decodeURIComponent(cookie.substring(name.length + 1))
        break
      }
    }
  }
  return cookieValue
}

// 初始化函数
const initializePhone = () => {
  // 优先使用props.phoneNumber
  phoneNum.value = props.phoneNumber || getQueryString("phoneNum")
  console.log('phoneNum.value', phoneNum.value);
  
  const formatterPhone = phoneNum.value.replace(/(\d{3})(\d{4})(\d{3})/, '$1-$2-$3')
  displayPhone.value = hidePhone(formatterPhone)

  const quicksoft_ticket = getCookie("bigscreenlogincode")
  console.log(quicksoft_ticket)

  if (quicksoft_ticket == 'tycgj') {
    callPhoneNo.value = '96418'
    CallWorkNo.value = '6003'
  } else if (quicksoft_ticket == 'tyszf') {
    callPhoneNo.value = '96419'
    CallWorkNo.value = '6004'
  } else if (quicksoft_ticket == 'typgzx') {
    callPhoneNo.value = '96417'
    CallWorkNo.value = '6005'
  } else {
    callPhoneNo.value = '96420'
    CallWorkNo.value = '6002'
  }
}

// WebSocket相关函数

// 点击拨号,拨号前先连接
const callout = async () => {
  try {
    // 执行WebSocket连接
    const result = await checkWebSocketConnection()
    if (result === 'success') {
      // 连接成功后的操作
      writeToScreen("CONNECTED成功")
      // 调用函数发送多个消息
      await sendMultipleMessages()
    } else {
      writeToScreen('<span style="color: red;">ERROR:websock连接失败</span>')
    }
  } catch (error) {
    writeToScreen('<span style="color: red;">CONNECTED连接ERROR:</span> ' + JSON.stringify(error))
  }
}

// websock连接
const checkWebSocketConnection = () => {
  showOutput.value = true
  socket.value = new WebSocket('ws://127.0.0.1:1818/')
  return new Promise((resolve, reject) => {
    socket.value.onopen = function () {
      resolve('success')
      console.log("websocket conn success")
    }
    socket.value.onerror = function (error) {
      reject(error)
    }
  })
}

// 打印结果
const writeToScreen = (message) => {
  outputMessages.value.push(message)
}

// 执行send
const sendMessage = (message) => {
  return new Promise((resolve, reject) => {
    socket.value.onmessage = function (event) {
      resolve(event.data)
    }
    socket.value.onerror = function (error) {
      reject(error)
    }
    socket.value.send(message)
  })
}

const sendMultipleMessages = async () => {
  try {
    // 嵌入
    writeToScreen(`SENT: {"Cmd":"Login","WorkNo":${CallWorkNo.value},"PhoneNo":"${callPhoneNo.value}","Password":"${CallWorkNo.value}","AgentType":2}`)
    const response1 = await sendMessage(`{"Cmd":"Login","WorkNo":${CallWorkNo.value},"PhoneNo":"${callPhoneNo.value}","Password":"${CallWorkNo.value}","AgentType":2}`)
    console.log(`{"Cmd":"Login","WorkNo":${CallWorkNo.value},"PhoneNo":"${callPhoneNo.value}","Password":"${CallWorkNo.value}","AgentType":2}`)
    writeToScreen('<span style="color: orange;">RESPONSE: ' + response1 + '</span>')

    // 拨号
    setTimeout(() => {
      console.log(`拨号SENT: {"Cmd":"CallOut","Caller":"8612319","Called":"${phoneNum.value}"}`)
      writeToScreen(`SENT: {"Cmd":"CallOut","Caller":"8612319","Called":"${phoneNum.value}"}`)
      const response2 = sendMessage(`{"Cmd":"CallOut","Caller":"8612319","Called":"${phoneNum.value}"}`)
      console.log('拨号RESPONSE:', response2)
      writeToScreen('<span style="color: orange;">RESPONSE: ' + response2 + '</span>')
    }, 5000)
  } catch (error) {
    writeToScreen('<span style="color: red;">ERROR:</span> ' + JSON.stringify(error))
  }
}

// 挂断
const wsexit = async () => {
  try {
    // 迁出
    writeToScreen('SENT: {"Cmd":"Logout"}')
    const response3 = await sendMessage('{"Cmd":"Logout"}')
    writeToScreen('<span style="color: orange;">RESPONSE: ' + response3 + '</span>')
    // websock关闭
    alert('websocket已迁出')
  } catch (error) {
    console.error('退出时发生错误:', error)
  }
}

// 组件挂载时初始化
onMounted(() => {
  initializePhone()
})

// 支持props变化时自动更新
watch(() => props.phoneNumber, (newVal) => {
  if (newVal) {
    phoneNum.value = newVal
    const formatterPhone = phoneNum.value.replace(/(\d{3})(\d{4})(\d{3})/, '$1-$2-$3')
    displayPhone.value = hidePhone(formatterPhone)
  }
})
</script>

<style scoped>
body {
  color: #fff;
}

.call-page {
  font-size: 25px;
  color: #fff;
  text-align: center;
  margin-top: 50px;
}

.chuzhiimg {
  width: 80px;
  height: 80px;
  margin-left: 240px;
  cursor: pointer;
}

.callBtn {
  position: absolute;
  bottom: 50px;
}

.output {
  height: 408px;
  overflow-y: auto;
  color: #fff;
}
</style>