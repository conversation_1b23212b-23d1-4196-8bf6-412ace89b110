# assistantdecision.vue 右侧表格到 RollupEffect.vue 数据传递功能

## 功能概述

已成功实现从 `assistantdecision.vue` 中的右侧表格点击事件（`handleClickTableRight`）到 `RollupEffect.vue` 组件的数据传递功能，完全参照 `rootmap.vue` 中的使用方法。

## 支持的右侧表格

### 1. 右侧易涝点报警表格
- **数据源**: `yldbjtbDataRight`
- **点击事件**: `@click="handleClickTableRight(item)"`
- **数据字段**: `type`, `forecastTime`, `waterDepth`, `waterTime`, `alarmLevel`, `coordinate`

### 2. 右侧物资调配统计表格  
- **数据源**: `wzdptjDatatbRight`
- **点击事件**: `@click="handleClickTableRight(item)"`
- **数据字段**: `name`, `quantity`, `responsible_person`, `response_time`, `coordinate`

### 3. 右侧移动泵车统计表格
- **数据源**: `bcddtjDatatbRight` 
- **点击事件**: `@click="handleClickTableRight(item)"`
- **数据字段**: `name`, `responsible_person`, `response_time`, `coordinate`

## 实现细节

### assistantdecision.vue 修改
```javascript
// 右侧表格点击处理
const handleClickTableRight = item => {
  console.log(item, '右侧表格item')
  // 传递右侧表格数据给父组件，父组件会将数据传递给RollupEffect组件
  emit('clickTableRight', { tableData: item })
}

// 事件定义中包含 clickTableRight
const emit = defineEmits([
  'tagClicked', 'clickTable', 'yuntuClickShow', 'clickScene1', 
  'clickScene', 'clickDate', 'fangxunrenyuanClickemit', 'clickTableRight'
])
```

### rootMap.vue 修改
```vue
<!-- 监听右侧表格点击事件 -->
<assistantdecision
  v-if="activeNavButton === 3"
  @tagClicked="handleTagData"
  @clickTable="handleClickTable"
  @clickTableRight="handleClickTable"  <!-- 新增 -->
  @yuntuClickShow="yuntuClickShowP"
  @clickDate="handleDate"
  @fangxunrenyuanClickemit="handlePeople"
/>
```

### RollupEffect.vue 增强
```javascript
// 自动识别数据来源类型
const dataSource = tableData.wzname ? '物资调配' : 
                  tableData.bcname ? '泵车调配' : 
                  tableData.type ? '易涝点报警' : '未知类型'
console.log('RollupEffect: 数据来源类型:', dataSource)
```

## 数据传递流程

1. **用户点击右侧表格行** → 触发 `handleClickTableRight(item)`
2. **assistantdecision.vue** → 发出 `clickTableRight` 事件
3. **rootMap.vue** → 接收事件并调用 `handleClickTable` 方法
4. **rootMap.vue** → 判断当前在辅助决策页面，将数据存储到 `rollupEffectTableData`
5. **RollupEffect.vue** → 通过 props 接收数据并处理

## 功能特性

- ✅ **统一处理**: 左侧和右侧表格都使用相同的 `handleClickTable` 方法处理
- ✅ **数据识别**: 自动识别数据类型（物资/泵车/易涝点）
- ✅ **坐标支持**: 支持坐标数据解析和地图标记
- ✅ **相机聚焦**: 自动聚焦到标记点位置
- ✅ **错误处理**: 完整的错误处理机制

## 使用方法

1. 进入辅助决策页面（导航第4项）
2. 点击右侧任意表格行
3. 查看控制台输出确认数据传递
4. 如果数据包含坐标，在数字孪生场景中查看标记点

## 验证状态

所有右侧表格已正确配置：
- ✅ 右侧易涝点报警表格 (第579行)
- ✅ 右侧物资调配统计表格 (第748行)  
- ✅ 右侧移动泵车统计表格 (第780行)

**功能已完全实现并可正常使用！**
