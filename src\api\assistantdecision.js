import request from '../utils/request'

// 左边
export function getDrainageData() {
  return request({
    url: '/taiyuan/psfltj-last/stat',
    method: 'get'
  })
}

export function getAlarmevent() {
  return request({
    url: '/taiyuan/bjsjlxtj-last/stat',
    method: 'get'
  })
}
export function getPersonnelDeployment() {
  return request({
    url: '/taiyuan/rydptj-last/stat',
    method: 'get'
  })
}
export function getImpactofwaterlogging() {
  return request({
    url: '/taiyuan/nlyxfx-last/stat',
    method: 'get'
  })
}
export function getMaterialAllocation() {
  return request({
    url: '/taiyuan/wzdptj-last/stat',
    method: 'get'
  })
}

export function getAccumulatedFlood(name) {
  return request({
    url: name ? `/taiyuan/jltj-last/stat?name=${name}` : '/taiyuan/jltj-last/stat?name=积涝统计-上一次',
    method: 'get'
  })
}

export function getEquipmentAlarm() {
  return request({
    url: '/taiyuan/sbbj-last/stat',
    method: 'get'
  })
}
export function getEquipmentMaintenance() {
  return request({
    url: '/taiyuan/sbbjqk-last/stat',
    method: 'get'
  })
}

// 右边
export function getDrainageDataRight() {
  return request({
    url: '/taiyuan/psfltj-history/stat',
    method: 'get'
  })
}

export function getAlarmeventRight() {
  return request({
    url: '/taiyuan/bjsjlxtj-history/stat',
    method: 'get'
  })
}
export function getPersonnelDeploymentRight() {
  return request({
    url: '/taiyuan/rydptj-history/stat',
    method: 'get'
  })
}
export function getImpactofwaterloggingRight() {
  return request({
    url: '/taiyuan/nlyxfx-history/stat',
    method: 'get'
  })
}
export function getMaterialAllocationRight() {
  return request({
    url: '/taiyuan/wzdptj-history/stat',
    method: 'get'
  })
}

export function getAccumulatedFloodRight(name) {
  return request({
    url: name ? `/taiyuan/jltj-history/stat?name=${name}` : '/taiyuan/jltj-history/stat',
    method: 'get'
  })
}

export function getEquipmentAlarmRight() {
  return request({
    url: '/taiyuan/sbbj-history/stat',
    method: 'get'
  })
}
export function getEquipmentMaintenanceRight() {
  return request({
    url: '/taiyuan/sbbjqk-history/stat',
    method: 'get'
  })
}

export function getDeviceTable(name) {
  return request({
    url: `/taiyuan/sbbjqk-last/stat?name=${name}`,
    method: 'get'
  })
}
export function getDeviceTableRight(name) {
  return request({
    url: `/taiyuan/sbbjqk-history/stat?name=${name}`,
    method: 'get'
  })
}

export function getDateData() {
  return request({
    url: '/taiyuan/fzjc-jyrl/stat',
    method: 'get'
  })
}

// 降雨日历-排水防涝统计
export function getRainfallPsfl(name) {
  return request({
    url: `/taiyuan/psfltj-jyrl/stat?name=${name}`,
    method: 'get'
  })
}

// 降雨日历-报警事件类型统计
export function getRainfallBjsj(name) {
  return request({
    url: `/taiyuan/bjsjlxtj-jyrl/stat?name=${name}`,
    method: 'get'
  })
}

// 降雨日历-人员调配统计
export function getRainfallRydp(name) {
  return request({
    url: `/taiyuan/rydptj-jyrl/stat?name=${name}`,
    method: 'get'
  })
}


// 降雨日历-内涝影响分析
export function getRainfallNlyx(name) {
  return request({
    url: `/taiyuan/nlyxfx-jyrl/stat?name=${name}`,
    method: 'get'
  })
}

// 降雨日历-积涝统计
export function getRainfallJltj(name) {
  return request({
    url: `/taiyuan/jltj-jyrl/stat?name=${name}`,
    method: 'get'
  })
}


// 降雨日历-物资调配统计
export function getRainfallWzdp(name) {
  return request({
    url: `/taiyuan/wzdptj-jyrl/stat?name=${name}`,
    method: 'get'
  })
}

// 降雨日历-设备报警
export function getRainfallSbbj(name) {
  return request({
    url: `/taiyuan/sbbj-jyrl/stat?name=${name}`,
    method: 'get'
  })
}


// 降雨日历-设备报警情况
export function getRainfallSbbjqk(name) {
  return request({
    url: `/taiyuan/sbbjqk-jyrl/stat?name=${name}`,
    method: 'get'
  })
}

export function yjdwGETperson(pageNum, pageSize, leader, leaderPhone) {
  return request({
    url: `/taiyuan/yjdw/list?pageSize=${pageSize}&pageNum=${pageNum}&leader=${leader}&leaderPhone=${leaderPhone}`,
    method: 'get'
  })
}





