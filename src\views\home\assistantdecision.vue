<template>
  <div class="screen-container">
    <div class="bg-layer bg-layer-1"></div>
    <div class="bg-layer bg-layer-2"></div>
    <!-- <div class="bg-layer bg-layer-3"></div> -->
    <!-- 地图容器，修改ID避免重复 -->
    <!-- <div id="player" class="map-container"></div> -->

    <!-- 左侧区域容器 -->
    <div class="left-container">
      <div class="left-section">
        <div class="time-weather">
          <div class="time-date">
            <div class="time">{{ currentTime }}</div>
            <div class="date">{{ currentDate }}</div>
          </div>
          <div class="divider"></div>
          <div class="weather">
            <!-- <img class="weather-icon" :src="getAssetsFile('sunny.png')" alt="天气" /> -->
            <!-- <img class="weather-icon1" :src="getAssetsFile('wendu.png')" alt="温度" /> -->
            <!-- <span class="temperature">17℃</span> -->
          </div>
        </div>
        <!-- 第一部分 -->
        <div class="content-layout-first">
          <div class="content-layout-header">
            <div class="weather-info-bar">
              <div class="current-weather">
                <span class="weather-label">当前天气</span>
                <div class="city-info">
                  <img class="location-icon" src="@/assets/images/home/<USER>" alt="" />
                  <span class="city-name"
                    >{{ weaherData.city }} &nbsp;&nbsp;{{ weaherData.minTemperature }}~{{
                      weaherData.maxTemperature
                    }}</span
                  >
                </div>
              </div>
              <img class="divider-line" src="@/assets/images/home/<USER>" alt="分隔线" />
              <div class="weather-detail">
                <div class="weather-icon-large">
                  <img src="@/assets/images/home/<USER>" alt="大雨" />
                  <div>{{ weaherData.daytimeWind }}，{{ weaherData.daytimeWeather }}</div>
                </div>
                <div class="weather-icon-large">
                  <img src="@/assets/images/home/<USER>" alt="大雨" />
                  <div>{{ weaherData.nighttimeWind }}，{{ weaherData.nighttimeWeather }}</div>
                </div>
              </div>
              <img class="divider-line" src="@/assets/images/home/<USER>" alt="分隔线" />
              <div class="weather-forecast">
                <div class="forecast-item">
                  <div class="forecast-title">未来24h天气</div>
                  <div class="forecast-value1">
                    <span class="forecast-value-num">{{ weaherData.forecast_24h }}</span>
                    <!-- <span class="forecast-value-unit">mm</span> -->
                  </div>
                </div>
                <div class="forecast-item">
                  <div class="forecast-title">检测次数</div>
                  <div class="forecast-value1">
                    <span class="forecast-value-num">{{ weaherData.monitor_count }}</span>
                    <!-- <span class="forecast-value-unit">mm</span> -->
                  </div>
                </div>
                <div class="forecast-item">
                  <div class="forecast-title">预警等级</div>
                  <div class="forecast-value1">
                    <span class="forecast-value-num">{{ weaherData.monitor_level }}</span>
                    <!-- <span class="forecast-value-unit">mm</span> -->
                  </div>
                </div>
              </div>
              <img class="divider-line" src="@/assets/images/home/<USER>" alt="分隔线" />

              <div class="publish-info" @click="yuntuClick">
                <span class="publish-label">云图</span>
                <div class="publish-times">
                  <!-- <div class="time-item">开始时间: 20:50:00</div> -->
                  <!-- <div class="time-item">结束时间: 18:00:00</div> -->
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 上下布局容器 -->
        <div class="left-content">
          <div class="content-layout-second">
            <div class="second-module-1">
              <div class="module-item-1">
                <div class="module-header">
                  <div class="module-time" @click="showDatePicker = !showDatePicker">降雨日历</div>
                  <h3 class="module-title">降雨积涝总览</h3>
                  <!-- 日期选择器 -->
                  <div v-if="showDatePicker" class="date-picker-container">
                    <el-date-picker
                      :key="datePickerKey"
                      v-model="selectedDate"
                      type="date"
                      placeholder="选择日期"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      :disabled-date="disabledDate"
                      @change="handleDateChange"
                      @blur="showDatePicker = false"
                      style="width: 200px"
                    >
                      <template #default="cell">
                        <div
                          class="cell"
                          :class="{
                            current: cell.isCurrent,
                            'has-rain': hasValidPrecipitation(cell),
                            disabled: isDateDisabled(cell)
                          }"
                        >
                          <span class="text">{{ cell.text }}</span>
                          <span v-if="hasValidPrecipitation(cell)" class="holiday" />
                          <span v-if="getPrecipitation(cell)" class="precipitation"
                            >{{ getPrecipitation(cell) }}mm</span
                          >
                        </div>
                      </template>
                    </el-date-picker>
                  </div>
                </div>
                <div class="module-content">
                  <div class="stats-row">
                    <div class="stats-col stats-col-1">
                      <div class="stats-label">雨量统计</div>
                      <div class="stats-card">
                        <div class="stats-icon">
                          <img :src="getAssetsFileRight('jiangyu.png')" alt="雨量图标" />
                        </div>
                        <div class="stats-data">
                          <div class="stats-unit">预报降雨 (mm)</div>
                          <div class="stats-value">{{ jyjlData?.forecast_rainfall || '0' }}</div>
                        </div>
                      </div>
                      <div class="stats-card">
                        <div class="stats-icon">
                          <img :src="getAssetsFileRight('jiangyu.png')" alt="雨量图标" />
                        </div>
                        <div class="stats-data">
                          <div class="stats-unit">实测降雨 (mm)</div>
                          <div class="stats-value">{{ jyjlData?.real_rainfall || '0' }}</div>
                        </div>
                      </div>
                    </div>
                    <div class="stats-col stats-col-2">
                      <!-- <div class="stats-label">
                        <div class="title">分区统计</div>
                        <div class="time">日/月/周</div>
                      </div> -->
                      <div class="venn-diagram-container">
                        <div class="venn-diagram">
                          <!-- 三个圆形 -->
                          <div class="circle circle-1" @click="handleCircleClick(1)">
                            <div class="circle-label top-left">报警事件点数{{ jyjlData?.alarm_event }} (个)</div>
                          </div>
                          <div class="circle circle-2" @click="handleCircleClick(2)">
                            <div class="circle-label top-right">预报内容点数{{ jyjlData?.forecast_point }} (个)</div>
                          </div>
                          <div class="circle circle-3" @click="handleCircleClick(3)">
                            <div class="circle-label bottom">监测事件点数{{ jyjlData?.monitor_point }} (个)</div>
                          </div>

                          <!-- 重叠区域的数字 -->
                          <div class="overlap-number overlap-left">{{ jyjlData?.alarm_forecast }}</div>
                          <div class="overlap-number overlap-right">{{ jyjlData?.forecast_monitor }}</div>

                          <div class="overlap-number overlap-bottom">{{ jyjlData?.alarm_monitor }}</div>
                          <div class="overlap-number overlap-center">{{ jyjlData?.alarm_forecast_monitor }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="module-item-2">
                <div class="module-header">
                  <h3 class="title" @click="openYldbjDialog" style="cursor: pointer; color: #fff; font-size: 24px">
                    更多
                  </h3>
                </div>
                <div class="color-level">
                  <div class="level-item level-1" @click="handleLevelClick('一级')"></div>
                  <div class="level-item level-2" @click="handleLevelClick('二级')"></div>
                  <div class="level-item level-3" @click="handleLevelClick('三级')"></div>
                  <div class="level-item level-4" @click="handleLevelClick('四级')"></div>
                </div>
                <div class="chart-container new-add-border">
                  <div class="low-area-table">
                    <table>
                      <thead>
                        <tr>
                          <th>名称</th>
                          <th>发生时间</th>
                          <th>积水深度(m)</th>
                          <th>持续时间(min)</th>
                          <th>报警等级</th>
                          <th>预报等级</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="(item, index) in yldbjtbData" :key="index" @click="handleClickTable(item)">
                          <td>{{ item.type }}</td>
                          <td>{{ item.forecastTime }}</td>
                          <td>{{ item.waterDepth }}</td>
                          <td>{{ item.waterTime }}</td>
                          <td>{{ item.reportLevel }}</td>
                          <td>{{ item.alarmLevel }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div class="leftbei"></div>
                  <div class="righttbei"></div>
                  <div class="bottombeibg bottom-left"></div>
                </div>
              </div>
            </div>
            <div class="second-module-2">
              <div class="module-2-top">
                <div class="module-2-top-left">
                  <div class="top-title-area">
                    <h3 class="area-title">调度指令统计</h3>
                  </div>
                  <div class="charts-container">
                    <div class="charts-container-left">
                      <div class="chart-item">
                        <div class="chart-title">调度总数</div>
                        <div class="chart-subtitle">{{ total_adjustments }}次</div>
                      </div>
                      <div class="chart-item">
                        <div class="chart-title">预警调度总数</div>
                        <div class="chart-subtitle">{{ forecast_adjustments }}次</div>
                      </div>
                      <div class="chart-item">
                        <div class="chart-title">雨中调度总数</div>
                        <div class="chart-subtitle">{{ rain_adjustments }}次</div>
                      </div>
                    </div>
                    <div class="charts-container-right">
                      <div class="chart-wrapper">
                        <div id="dispatch-chart-1" class="dispatch-chart"></div>
                      </div>
                      <div class="chart-wrapper">
                        <div id="dispatch-chart-2" class="dispatch-chart"></div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="module-2-top-right">
                  <div class="top-title-area">
                    <h3 class="area-title">排水设施调度及执行情况统计</h3>
                    <span class="title-tip">防汛简报</span>
                  </div>
                  <div class="module-2-top-right-tb">
                    <table>
                      <thead>
                        <!-- 第一级表头：总抽排量和总调蓄量 -->
                        <tr>
                          <th class="level-1" colspan="4">总抽排量 (400m³/s)</th>
                          <th class="level-1" colspan="4">总调蓄量 (万m³)</th>
                        </tr>

                        <!-- 第二级表头：泵站、排洪渠、缓洪池 -->
                        <tr>
                          <th class="level-2" colspan="4">泵站</th>
                          <th class="level-2" colspan="2">排洪渠</th>
                          <th class="level-2" colspan="2">缓洪池</th>
                        </tr>

                        <!-- 第三级表头：具体参数分类（已移除开启台数） -->
                        <tr>
                          <th class="level-3" colspan="2">规模 (m³/s)开启台数</th>
                          <th class="level-3" colspan="2">开启时间</th>
                          <th rowspan="2">调度水位 (m)</th>
                          <th rowspan="2">实际水位 (m)</th>
                          <th class="level-3" colspan="2">缓洪时间 (hr)</th>
                        </tr>

                        <!-- 第四级表头：仅针对需要的参数 -->
                        <tr>
                          <th>调度</th>
                          <th>实际</th>
                          <th>调度</th>
                          <th>实际</th>
                          <!-- 排洪渠的表头已合并到第三级 -->
                          <th></th>
                          <th></th>
                          <th>调度</th>
                          <th>实际</th>
                        </tr>
                      </thead>
                      <tbody>
                        <!-- 数据行1 -->
                        <tr v-for="(item, index) in psssData" :key="index">
                          <td>{{ item.pump_stations_started_scheduled }}</td>
                          <td>{{ item.pump_stations_started_actual }}</td>
                          <td>{{ item.pump_stations_time_scheduled }}</td>
                          <td>{{ item.pump_stations_time_actual }}</td>
                          <td>{{ item.drainage_channels_scheduled }}</td>
                          <td>{{ item.drainage_channels_actual }}</td>
                          <td>{{ item.buffer_pools_scheduled }}</td>
                          <td>{{ item.buffer_pools_actual }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
              <div class="module-2-bottom">
                <div class="bottom-title-area">
                  <h3 class="area-title">人员物资调度及执行情况统计</h3>
                </div>
                <div class="bottom-content-area">
                  <!-- 人员调配统计 -->
                  <div class="personnel-section new-add-border">
                    <div class="section-header">
                      <h4 class="section-title">
                        人员调配统计 {{ rywztotal_times || '0' }}次 平均响应时间:
                        {{ rywzaverage_response_time || '0' }}
                      </h4>
                    </div>
                    <div class="section-table">
                      <table>
                        <thead>
                          <tr>
                            <th>名称</th>
                            <th>负责人</th>
                            <th>队伍人数</th>
                            <th>响应时间</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(item, index) in rywzDatatb" :key="index" @click.stop="fangxunrenyuanClick(index)">
                            <td>{{ item.name || '清防一大队' }}</td>
                            <td>{{ item.responsible_person || '王xx' }}</td>
                            <td>{{ item.team_size || '3' }}</td>
                            <td>{{ item.response_time || '1小时' }}</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    <div class="leftbei"></div>
                    <div class="righttbei"></div>
                    <div class="bottombeibg bottom-left"></div>
                  </div>

                  <!-- 物资调配统计 -->
                  <div class="material-section new-add-border">
                    <div class="section-header">
                      <h4 class="section-title">
                        物资调配统计 {{ wzdptjtotal_times || '0' }}次 平均响应时间:
                        {{ wzdptjaverage_response_time || '0' }}
                      </h4>
                    </div>
                    <div class="section-table">
                      <table>
                        <thead>
                          <tr>
                            <th>名称</th>
                            <th>数量</th>
                            <th>负责人</th>
                            <th>响应时间</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(item, index) in wzdptjDatatb" :key="index" @click="handleClickTable(item)">
                            <td>{{ item.wzname || '沙袋' }}</td>
                            <td>{{ item.quantity || '2' }}</td>
                            <td>{{ item.responsible_person || '王xx' }}</td>
                            <td>{{ item.response_time || '1小时' }}</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    <div class="leftbei"></div>
                    <div class="righttbei"></div>
                    <div class="bottombeibg bottom-left"></div>
                  </div>

                  <!-- 移动泵车统计 -->
                  <div class="pump-section new-add-border">
                    <div class="section-header">
                      <h4 class="section-title">
                        移动泵车统计 {{ bcddtjtotal_times || '0' }}次 平均响应时间:
                        {{ bcddtjaverage_response_time || '0' }}
                      </h4>
                    </div>
                    <div class="section-table">
                      <table>
                        <thead>
                          <tr>
                            <th>名称</th>
                            <th>负责人</th>
                            <th>响应时间</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(item, index) in bcddtjDatatb" :key="index" @click="handleClickTable(item)">
                            <td>{{ item.bcname || '抽水泵' + (index + 1) }}</td>
                            <td>{{ item.responsible_person || '王xx' }}</td>
                            <td>{{ item.response_time || '1小时' }}</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    <div class="leftbei"></div>
                    <div class="righttbei"></div>
                    <div class="bottombeibg bottom-left"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 中间区域容器 -->
    <div class="middle-container">
      <div class="middle-section">
        <!-- 标题 -->
        <!-- <div class="section-title">内涝安全预警监测综合驾驶舱系统</div> -->
        <!-- 导航按钮 -->
        <!-- <div class="nav-buttons">
          <div
            v-for="(btn, index) in navButtons"
            :key="index"
            :class="['nav-button', { active: activeNavButton === index }]"
            @click="activeNavButton = index"
          >
            <div class="nav-button-text">{{ btn.text }}</div>
          </div>
        </div> -->
      </div>
    </div>

    <!-- 右侧区域容器 -->
    <div class="right-container">
      <div class="right-section">
        <!-- 上部分：用户信息和返回门户 -->
        <div class="user-portal-container">
          <!-- <div class="user-info">
            <img class="user-icon" src="https://picsum.photos/200/300" alt="用户" />
            <span class="username">管理员</span>
          </div>
          <div class="portal-back">
            <img class="portal-icon" src="https://picsum.photos/200/300" alt="门户" />
            <span class="portal-text">返回门户</span>
          </div> -->
        </div>

        <!-- 下部分：新增的内容区域 -->
        <div class="right-content">
          <div class="content-layout-second">
            <div class="second-module-1">
              <div class="module-item-1">
                <div class="module-header">
                  <div class="module-time" @click="showDatePickerRight = !showDatePickerRight">降雨日历</div>
                  <h3 class="module-title">事件统计</h3>
                  <!-- 日期选择器 -->
                  <div v-if="showDatePickerRight" class="date-picker-container">
                    <el-date-picker
                      :key="datePickerKeyRight"
                      v-model="selectedDateRight"
                      type="date"
                      placeholder="选择日期"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      :disabled-date="disabledDate"
                      @change="handleDateChangeRight"
                      @blur="showDatePickerRight = false"
                      style="width: 200px"
                    >
                      <template #default="cell">
                        <div
                          class="cell"
                          :class="{
                            current: cell.isCurrent,
                            'has-rain': hasValidPrecipitation(cell),
                            disabled: isDateDisabled(cell)
                          }"
                        >
                          <span class="text">{{ cell.text }}</span>
                          <span v-if="hasValidPrecipitation(cell)" class="holiday" />
                          <span v-if="getPrecipitation(cell)" class="precipitation"
                            >{{ getPrecipitation(cell) }}mm</span
                          >
                        </div>
                      </template>
                    </el-date-picker>
                  </div>
                </div>
                <div class="module-content">
                  <div class="stats-row">
                    <div class="stats-col stats-col-1">
                      <div class="stats-label">雨量统计</div>
                      <div class="stats-card">
                        <div class="stats-icon">
                          <img :src="getAssetsFileRight('jiangyu.png')" alt="雨量图标" />
                        </div>
                        <div class="stats-data">
                          <div class="stats-unit">预报降雨 (mm)</div>
                          <div class="stats-value">{{ jyjlDataRight?.forecast_rainfall || '0' }}</div>
                        </div>
                      </div>
                      <div class="stats-card">
                        <div class="stats-icon">
                          <img :src="getAssetsFileRight('jiangyu.png')" alt="雨量图标" />
                        </div>
                        <div class="stats-data">
                          <div class="stats-unit">实测降雨 (mm)</div>
                          <div class="stats-value">{{ jyjlDataRight?.real_rainfall || '0' }}</div>
                        </div>
                      </div>
                    </div>
                    <div class="stats-col stats-col-2">
                      <!-- <div class="stats-label">
                        <div class="title">分区统计</div>
                        <div class="time">日/月/周</div>
                      </div> -->
                      <div class="venn-diagram-container">
                        <div class="venn-diagram">
                          <!-- 三个圆形 -->
                          <div class="circle circle-1" @click="handleCircleClickRight(1)">
                            <div class="circle-label top-left">报警事件点数{{ jyjlDataRight?.alarm_event }} (个)</div>
                          </div>
                          <div class="circle circle-2" @click="handleCircleClickRight(2)">
                            <div class="circle-label top-right">
                              预报内容点数{{ jyjlDataRight?.forecast_point }} (个)
                            </div>
                          </div>
                          <div class="circle circle-3" @click="handleCircleClickRight(3)">
                            <div class="circle-label bottom">监测事件点数{{ jyjlDataRight?.monitor_point }} (个)</div>
                          </div>

                          <!-- 重叠区域的数字 -->
                          <div class="overlap-number overlap-left">{{ jyjlDataRight?.alarm_forecast }}</div>
                          <div class="overlap-number overlap-right">{{ jyjlDataRight?.forecast_monitor }}</div>

                          <div class="overlap-number overlap-bottom">{{ jyjlDataRight?.alarm_monitor }}</div>
                          <div class="overlap-number overlap-center">{{ jyjlDataRight?.alarm_forecast_monitor }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="module-item-2">
                <div class="module-header">
                  <h3 class="title" @click="openYldbjDialog" style="cursor: pointer; color: #fff; font-size: 24px">
                    更多
                  </h3>
                </div>
                <div class="color-level">
                  <div class="level-item level-1" @click="handleLevelClickRight('一级')"></div>
                  <div class="level-item level-2" @click="handleLevelClickRight('二级')"></div>
                  <div class="level-item level-3" @click="handleLevelClickRight('三级')"></div>
                  <div class="level-item level-4" @click="handleLevelClickRight('四级')"></div>
                </div>
                <div class="chart-container new-add-border">
                  <div class="low-area-table">
                    <table>
                      <thead>
                        <tr>
                          <th>名称</th>
                          <th>发生时间</th>
                          <th>积水深度(m)</th>
                          <th>持续时间(min)</th>
                          <th>报警等级</th>
                          <th>预报等级</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="(item, index) in yldbjtbDataRight" :key="index" @click="handleClickTableRight(item)">
                          <td>{{ item.type }}</td>
                          <td>{{ item.forecastTime }}</td>
                          <td>{{ item.waterDepth }}</td>
                          <td>{{ item.waterTime }}</td>
                          <td>{{ item.alarmLevel }}</td>
                          <td>{{ item.grade }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div class="leftbei"></div>
                  <div class="righttbei"></div>
                  <div class="bottombeibg bottom-left"></div>
                </div>
              </div>
            </div>
            <div class="second-module-2">
              <div class="module-2-top">
                <div class="module-2-top-left">
                  <div class="top-title-area">
                    <h3 class="area-title">调度指令统计</h3>
                  </div>
                  <div class="charts-container">
                    <div class="charts-container-left">
                      <div class="chart-item">
                        <div class="chart-title">调度总数</div>
                        <div class="chart-subtitle">{{ total_adjustmentsRight || '0' }}次</div>
                      </div>
                      <div class="chart-item">
                        <div class="chart-title">预警调度总数</div>
                        <div class="chart-subtitle">{{ forecast_adjustmentsRight || '0' }}次</div>
                      </div>
                      <div class="chart-item">
                        <div class="chart-title">雨中调度总数</div>
                        <div class="chart-subtitle">{{ rain_adjustmentsRight || '0' }}次</div>
                      </div>
                    </div>
                    <div class="charts-container-right">
                      <div class="chart-wrapper">
                        <div id="dispatch-chart-3" class="dispatch-chart"></div>
                      </div>
                      <div class="chart-wrapper">
                        <div id="dispatch-chart-4" class="dispatch-chart"></div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="module-2-top-right">
                  <div class="top-title-area">
                    <h3 class="area-title">排水设施调度及执行情况统计</h3>
                    <span class="title-tip">防汛简报</span>
                  </div>
                  <div class="module-2-top-right-tb">
                    <table>
                      <thead>
                        <!-- 第一级表头：总抽排量和总调蓄量 -->
                        <tr>
                          <th class="level-1" colspan="4">总抽排量 (400m³/s)</th>
                          <th class="level-1" colspan="4">总调蓄量 (万m³)</th>
                        </tr>

                        <!-- 第二级表头：泵站、排洪渠、缓洪池 -->
                        <tr>
                          <th class="level-2" colspan="4">泵站</th>
                          <th class="level-2" colspan="2">排洪渠</th>
                          <th class="level-2" colspan="2">缓洪池</th>
                        </tr>

                        <!-- 第三级表头：具体参数分类（已移除开启台数） -->
                        <tr>
                          <th class="level-3" colspan="2">规模 (m³/s)开启台数</th>
                          <th class="level-3" colspan="2">开启时间</th>
                          <th colspan="1" rowspan="2">调度水位 (m)</th>
                          <th colspan="1" rowspan="2">实际水位 (m)</th>
                          <th class="level-3" colspan="2">缓洪时间 (hr)</th>
                        </tr>

                        <!-- 第四级表头：仅针对需要的参数 -->
                        <tr>
                          <th>调度</th>
                          <th>实际</th>
                          <th>调度</th>
                          <th>实际</th>
                          <!-- 排洪渠的表头已合并到第三级 -->
                          <th></th>
                          <th></th>
                          <th>调度</th>
                          <th>实际</th>
                        </tr>
                      </thead>
                      <tbody>
                        <!-- 数据行 -->
                        <tr v-for="(item, index) in psssDataRight" :key="index">
                          <td>{{ item.pump_stations_started_scheduled }}</td>
                          <td>{{ item.pump_stations_started_actual }}</td>
                          <td>{{ item.pump_stations_time_scheduled }}</td>
                          <td>{{ item.pump_stations_time_actual }}</td>
                          <td>{{ item.drainage_channels_scheduled }}</td>
                          <td>{{ item.drainage_channels_actual }}</td>
                          <td>{{ item.buffer_pools_scheduled }}</td>
                          <td>{{ item.buffer_pools_actual }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
              <div class="module-2-bottom">
                <div class="bottom-title-area">
                  <h3 class="area-title">人员物资调度及执行情况统计</h3>
                </div>
                <div class="bottom-content-area">
                  <!-- 人员调配统计 -->
                  <div class="personnel-section new-add-border">
                    <div class="section-header">
                      <h4 class="section-title">
                        人员调配统计 {{ rywztotal_timesRight || '0' }}次 平均响应时间:
                        {{ rywzaverage_response_timeRight || '0' }}
                      </h4>
                    </div>
                    <div class="section-table">
                      <table>
                        <thead>
                          <tr>
                            <th>名称</th>
                            <th>负责人</th>
                            <th>队伍人数</th>
                            <th>响应时间</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr
                            v-for="(item, index) in rywzDatatbRight"
                            :key="index"
                            @click.stop="fangxunrenyuanClick(index)"
                          >
                            <td>{{ item.name || '清防一大队' }}</td>
                            <td>{{ item.responsible_person || '王xx' }}</td>
                            <td>{{ item.team_size || '3' }}</td>
                            <td>{{ item.response_time || '1小时' }}</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    <div class="leftbei"></div>
                    <div class="righttbei"></div>
                    <div class="bottombeibg bottom-left"></div>
                  </div>

                  <!-- 物资调配统计 -->
                  <div class="material-section new-add-border">
                    <div class="section-header">
                      <h4 class="section-title">
                        物资调配统计 {{ wzdptjtotal_timesRight || '0' }}次 平均响应时间:
                        {{ wzdptjaverage_response_timeRight || '0' }}
                      </h4>
                    </div>
                    <div class="section-table">
                      <table>
                        <thead>
                          <tr>
                            <th>名称</th>
                            <th>数量</th>
                            <th>负责人</th>
                            <th>响应时间</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(item, index) in wzdptjDatatbRight" :key="index" @click="handleClickTableRight(item)">
                            <td>{{ item.name || '沙袋' }}</td>
                            <td>{{ item.quantity || '2' }}</td>
                            <td>{{ item.responsible_person || '王xx' }}</td>
                            <td>{{ item.response_time || '1小时' }}</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    <div class="leftbei"></div>
                    <div class="righttbei"></div>
                    <div class="bottombeibg bottom-left"></div>
                  </div>

                  <!-- 移动泵车统计 -->
                  <div class="pump-section new-add-border">
                    <div class="section-header">
                      <h4 class="section-title">
                        移动泵车统计 {{ bcddtjtotal_timesRight || '0' }}次 平均响应时间:
                        {{ bcddtjaverage_response_timeRight || '0' }}
                      </h4>
                    </div>
                    <div class="section-table">
                      <table>
                        <thead>
                          <tr>
                            <th>名称</th>
                            <th>负责人</th>
                            <th>响应时间</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(item, index) in bcddtjDatatbRight" :key="index" @click="handleClickTableRight(item)">
                            <td>{{ item.name || '抽水泵' + (index + 1) }}</td>
                            <td>{{ item.responsible_person || '王xx' }}</td>
                            <td>{{ item.response_time || '1小时' }}</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    <div class="leftbei"></div>
                    <div class="righttbei"></div>
                    <div class="bottombeibg bottom-left"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 易涝点报警弹窗 -->
  <el-dialog
    modal-class="supplies-dialog"
    v-model="yldbjDialogVisible"
    title="易涝点报警"
    width="1500"
    :modal="true"
    :draggable="true"
    :before-close="handleYldbjDialogClose"
    class="custom-dialog supplies-dialog"
  >
    <div style="margin-top: 40px; font-size: 24px">
      <el-form size="large" :inline="true" :model="formInlineYldbj" class="demo-form-inline" style="margin-left: 10px">
        <el-form-item label="区域">
          <el-select
            style="width: 300px"
            :popper-append-to-body="true"
            popper-class="select-popper"
            v-model="formInlineYldbj.area"
            placeholder="请选择区域"
            clearable
          >
            <el-option :label="item.name" :key="item.id" v-for="item in yldbjOptions" :value="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item label="类型">
          <el-select
            style="width: 300px"
            :popper-append-to-body="true"
            popper-class="select-popper"
            v-model="formInlineYldbj.type"
            placeholder="请选择类型"
            clearable
          >
            <el-option :label="item.name" :key="item.value" v-for="item in yldbjTypeOptions" :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSubmitYldbj">查询</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="yldbjTableData" height="700" style="width: 100%; margin-bottom: 32px; background: transparent">
        <el-table-column prop="type" label="名称" />
        <el-table-column prop="forecastTime" label="发生时间" />
        <el-table-column prop="waterDepth" label="积水深度(m)" />
        <el-table-column prop="waterTime" label="持续时间(min)" />
        <el-table-column prop="alarmLevel" label="报警等级" />
        <el-table-column prop="grade" label="预报等级" />
      </el-table>

      <el-pagination
        v-model:current-page="yldbjPageNum"
        v-model:page-size="yldbjPageSize"
        :page-sizes="[20, 40, 80, 100, 200]"
        size="large"
        layout="total, prev, pager, next, jumper"
        :total="yldbjTotal"
        @size-change="handleSizeChangeYldbj"
        @current-change="handleCurrentChangeYldbj"
      />
    </div>
  </el-dialog>

  <el-dialog
    :modal="false"
    :draggable="true"
    modal-class="supplies-dialog fangxunrenyuan"
    v-model="fangxunrenyuandialogVisible"
    title="防汛人员"
    :show-close="false"
    width="1500"
  >
    <div class="leftbei"></div>
    <div class="righttbei"></div>
    <div class="bottombeibg"></div>
    <div style="margin-top: 40px; font-size: 24px">
      <div class="heaader-close" @click="fangxunrenyuandialogVisible = false">x</div>
      <el-form
        size="large"
        :inline="true"
        :model="formInlinefangxunrenyuan"
        class="demo-form-inline"
        style="margin-left: 10px"
      >
        <el-form-item label="负责人">
          <el-input style="width: 300px" v-model="formInlinefangxunrenyuan.leader" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="负责人联系电话">
          <el-input
            style="width: 300px"
            v-model="formInlinefangxunrenyuan.leaderPhone"
            placeholder="请输入"
            clearable
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSubmitfangxunrenyuan">查询</el-button>
        </el-form-item>
      </el-form>
      <el-table
        :data="fangxunrenyuantableData"
        height="650"
        :cell-style="{ textAlign: 'center' }"
        :header-cell-style="{ 'text-align': 'center' }"
        @row-click="handleClickfangxuanrenyuan"
        @cell-click="handleCellClick"
        style="width: 100%; margin-bottom: 32px; background: transparent"
      >
        <el-table-column prop="teamName" label="队伍名称" />
        <el-table-column prop="teamDuty" label="队伍职责" />
        <el-table-column prop="leader" label="负责人" />
        <el-table-column prop="leaderPhones" label="负责人联系电话" />
        <el-table-column prop="responsibleUnit" label="责任单位" />
        <el-table-column prop="reservePointAddress" label="地址" />
        <el-table-column prop="locationArea" label="所在区域" />
      </el-table>

      <el-pagination
        v-model:current-page="fangxunrenyuanPageNum"
        v-model:page-size="fangxunrenyuanPageSize"
        :page-sizes="[20, 40, 80, 100, 200]"
        size="large"
        layout="total, prev, pager, next, jumper"
        :total="fangxunrenyuanTotal"
        @size-change="handleSizeChangefangxunrenyuan"
        @current-change="handleCurrentChangefangxunrenyuan"
      />
    </div>
  </el-dialog>
  <el-dialog
    modal-class="supplies-dialog"
    v-model="callPhonedialogVisible"
    title=""
    width="1000"
    :modal="true"
    :draggable="true"
    :show-close="false"
    class="custom-dialog supplies-dialog"
  >
    <div style="margin-top: 40px; font-size: 24px; height: 600px">
      <div class="heaader-close" @click="callPhonedialogVisible = false">x</div>
      <callPhone :phoneNumber="phoneNumber" />
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'
import { Calendar } from '@element-plus/icons-vue'
import { getLocation, getLocation2 } from '@/api/home'
import { cstqGET } from '@/api/intelligence'
import callPhone from '@/components/callPhone.vue'
import {
  yjdwGETperson,
  getDrainageData,
  getAlarmevent,
  getPersonnelDeployment,
  getImpactofwaterlogging,
  getMaterialAllocation,
  getAccumulatedFlood,
  getEquipmentAlarm,
  getEquipmentMaintenance,
  getDrainageDataRight,
  getAlarmeventRight,
  getPersonnelDeploymentRight,
  getImpactofwaterloggingRight,
  getMaterialAllocationRight,
  getAccumulatedFloodRight,
  getEquipmentAlarmRight,
  getEquipmentMaintenanceRight,
  getDeviceTable,
  getDeviceTableRight,
  getDateData
} from '@/api/assistantdecision'

import {
  getDdzltj,
  getPsss,
  getRywz,
  getWzdptj,
  getBcddtj,
  getYldbjtb,
  getJyjlzl,
  // 右边接口
  getDdzrtjRight,
  getRydptjRight,
  getWzdptjRight,
  getBcdptjRight,
  getPsssddtjRight,
  getJyjlzlRight,
  // 降雨日历接口
  getjyjltjDate,
  getddzltjDate,
  getrydptjDate,
  getwzdptjDate,
  getydbcdptjDate,
  getpsssddtjDate,
  getYldbjtbDate,
  getvetData
} from '@/api/assistantdecision-new'

// 韦恩图数据 - 左侧
const vennDiagramData = ref({
  alarmEvents: 50,
  forecastContent: 50,
  monitorEvents: 50,
  overlapLeft: 20,
  overlapRight: 20,
  overlapBottom: 20,
  overlapCenter: 20
})

const fangxunrenyuandialogVisible = ref(false)
const formInlinefangxunrenyuan = ref({
  leader: '',
  leaderPhone: ''
})
const fangxunrenyuantableData = ref([])
const fangxunrenyuanTotal = ref(0)
const fangxunrenyuanPageSize = ref(20)
const fangxunrenyuanPageNum = ref(1)
const onSubmitfangxunrenyuan = () => {
  fangxunrenyuanF()
}
const fangxunrenyuanClick = index => {
  fangxunrenyuanF(index)
}
const fangxunrenyuanF = (index = undefined) => {
  yjdwGETperson(
    fangxunrenyuanPageNum.value,
    fangxunrenyuanPageSize.value,
    formInlinefangxunrenyuan.value.leader,
    formInlinefangxunrenyuan.value.leaderPhone
  ).then(res => {
    console.log(res, '点击防汛人员', res.rows)
    res.rows.forEach(resData => {
      resData.leaderPhones =
        resData.leaderPhone.slice(0, 3) + '****' + resData.leaderPhone.slice(resData.leaderPhone.length - 4)
    })
    fangxunrenyuantableData.value = res.rows
    emit('fangxunrenyuanClickemit', { tagData: { indexdx: index }, locationData: res.rows })
    fangxunrenyuanTotal.value = res.total
    fangxunrenyuandialogVisible.value = true
  })
}
const handleSizeChangefangxunrenyuan = size => {
  fangxunrenyuanPageNum.value = 1
  fangxunrenyuanPageSize.value = size
  fangxunrenyuanF()
}
const handleCurrentChangefangxunrenyuan = page => {
  fangxunrenyuanPageNum.value = page
  fangxunrenyuanF()
}
const phoneNumber = ref('')
const callPhonedialogVisible = ref(false)
const handleClickfangxuanrenyuan = row => {
  console.log(row, '点击弹窗中人员单个数据')
  fdapi.marker.focus('FXEY_' + row.id, 1200, 0)
}
const handleCellClick = (row, column, cell, event) => {
  if (column.property === 'leaderPhones') {
    phoneNumber.value = row.leaderPhone
    callPhonedialogVisible.value = true
  }
  console.log(row, column, cell, event, '点击某一个单元格')
}

// 降雨积涝总览
const jyjlData = ref(null)
const getjyjlData = async () => {
  try {
    const result = await getJyjlzl()
    // console.log("🚀 ~ getjyjlData ~ result:", result)

    if (result) {
      if (result.code === 200) {
        // jyjlData.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        jyjlData.value = JSON.parse(result.data[0].value)
        console.log('降雨积涝总览', jyjlData.value)
      }
    } else {
      console.error('降雨积涝总览数据格式不正确:', result)
    }
  } catch (error) {
    console.error('降雨积涝总览数据失败:', error)
  }
}
const jyjlDataRight = ref(null)
const getjyjlDataRight = async () => {
  try {
    const result = await getJyjlzlRight()
    console.log('🚀 ~ getjyjlData ~ result:', result)

    if (result) {
      if (result.code === 200) {
        // jyjlData.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        jyjlDataRight.value = JSON.parse(result.data[0].value)
        console.log('降雨积涝总览', jyjlDataRight.value)
      }
    } else {
      console.error('降雨积涝总览数据格式不正确:', result)
    }
  } catch (error) {
    console.error('降雨积涝总览数据失败:', error)
  }
}

// 预警统计 - 左侧
const yldbjtbData = ref(null)
const getyldbjtbData = async () => {
  try {
    const result = await getYldbjtb({ pageNum: 1, pageSize: 20 })
    if (result) {
      if (result.code === 200) {
        yldbjtbData.value = result.rows
        console.log('易涝点报警表格:', yldbjtbData.value)
      }
    }
  } catch (error) {
    console.error('易涝点报警:', error)
  }
}

// 预警统计 - 右侧
const getyldbjtbDataRight = async () => {
  try {
    const result = await getYldbjtb({ pageNum: 1, pageSize: 20 })
    if (result) {
      if (result.code === 200) {
        yldbjtbDataRight.value = result.rows
        console.log('右侧易涝点报警表格:', yldbjtbDataRight.value)
      }
    }
  } catch (error) {
    console.error('右侧易涝点报警:', error)
  }
}

// 易涝点报警弹窗相关
const yldbjDialogVisible = ref(false)
const yldbjTotal = ref(0)
const yldbjPageNum = ref(1)
const yldbjPageSize = ref(20)
const yldbjTableData = ref([])
const yldbjSelectedDate = ref('') // 存储当前选择的日期
const formInlineYldbj = ref({
  area: '',
  type: ''
})
const yldbjOptions = ref([
  { name: '尖草坪区', id: 1 },
  { name: '杏花岭区', id: 2 },
  { name: '小店区', id: 3 },
  { name: '迎泽区', id: 4 },
  { name: '万柏林区', id: 5 },
  { name: '晋源区', id: 6 }
])
const yldbjTypeOptions = ref([
  { name: '低洼路段', value: '低洼路段' },
  { name: '人行下穿', value: '人行下穿' },
  { name: '车辆下穿', value: '车辆下穿' }
])

// 打开易涝点报警弹窗
const openYldbjDialog = () => {
  yldbjDialogVisible.value = true
  yldbjPage()
}

// 关闭易涝点报警弹窗
const handleYldbjDialogClose = () => {
  yldbjDialogVisible.value = false
}

// 获取易涝点报警数据
const yldbjPage = () => {
  const params = {
    pageNum: yldbjPageNum.value,
    pageSize: yldbjPageSize.value
  }

  // 添加查询条件
  if (formInlineYldbj.value.area) {
    params.area = formInlineYldbj.value.area
  }
  if (formInlineYldbj.value.type) {
    params.type = formInlineYldbj.value.type
  }

  // 根据是否选择了日期来决定调用哪个接口
  let apiCall
  if (yldbjSelectedDate.value) {
    // 如果选择了日期，调用降雨日历接口并传递分页参数
    apiCall = getYldbjtbDate(yldbjSelectedDate.value, params)
  } else {
    // 如果没有选择日期，调用默认接口
    apiCall = getYldbjtb(params)
  }

  apiCall
    .then(res => {
      console.log(res, '易涝点报警分页数据')
      if (res.code === 200) {
        yldbjTableData.value = res.rows || []
        yldbjTotal.value = res.total || 0
      }
    })
    .catch(error => {
      console.error('获取易涝点报警数据失败:', error)
      // 如果接口调用失败，使用当前页面的数据作为备用
      yldbjTableData.value = yldbjtbData.value || []
      yldbjTotal.value = yldbjTableData.value.length
    })
}

// 查询易涝点报警
const onSubmitYldbj = () => {
  console.log('查询条件:', formInlineYldbj.value)
  yldbjPageNum.value = 1
  yldbjPage()
}

// 分页大小改变
const handleSizeChangeYldbj = size => {
  yldbjPageSize.value = size
  yldbjPageNum.value = 1
  yldbjPage()
}

// 当前页改变
const handleCurrentChangeYldbj = page => {
  yldbjPageNum.value = page
  yldbjPage()
}

// 移动泵车调配统计 - 左边
const bcddtjData = ref(null)
const bcddtjtotal_times = ref(null)
const bcddtjaverage_response_time = ref(null)
const bcddtjDatatb = ref(null)
const getBcddtjData = async () => {
  try {
    const result = await getBcddtj()
    // console.log('调度指令统计', result)

    if (result) {
      if (result.code === 200) {
        bcddtjData.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        bcddtjtotal_times.value = bcddtjData.value[0].total_times
        bcddtjaverage_response_time.value = bcddtjData.value[0].average_response_time
        bcddtjDatatb.value = bcddtjData.value[0].pumps
        console.log('移动泵车调配统计', bcddtjData.value)
      } else {
        accumulatedFloodList.value = []
        console.error('物资调配统计数据格式不正确:', result)
      }
    }
  } catch (error) {
    console.error('更新降雨相关模块出错:', error)
  }
}

// 移动泵车调配统计 - 右边
const bcddtjDataRight = ref(null)
const bcddtjtotal_timesRight = ref(null)
const bcddtjaverage_response_timeRight = ref(null)
const bcddtjDatatbRight = ref(null)
const getBcddtjDataRight = async () => {
  try {
    const result = await getBcdptjRight()
    // console.log('右侧移动泵车调配统计', result)

    if (result) {
      if (result.code === 200) {
        bcddtjDataRight.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        bcddtjtotal_timesRight.value = bcddtjDataRight.value[0].total_times
        bcddtjaverage_response_timeRight.value = bcddtjDataRight.value[0].average_response_time
        bcddtjDatatbRight.value = bcddtjDataRight.value[0].pumps
        console.log('右侧移动泵车调配统计', bcddtjDataRight.value)
      } else {
        console.error('右侧移动泵车调配统计数据格式不正确:', result)
      }
    }
  } catch (error) {
    console.error('右侧移动泵车调配统计数据失败:', error)
  }
}

// 物资调配统计 - 左边
const wzdptjData = ref(null)
const wzdptjtotal_times = ref(null)
const wzdptjaverage_response_time = ref(null)
const wzdptjDatatb = ref(null)

const getWzdptjData = async () => {
  try {
    const result = await getWzdptj()
    // console.log('调度指令统计', result)

    if (result) {
      if (result.code === 200) {
        wzdptjData.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        wzdptjtotal_times.value = wzdptjData.value[0].total_times
        wzdptjaverage_response_time.value = wzdptjData.value[0].average_response_time
        wzdptjDatatb.value = wzdptjData.value[0].data
        console.log('物资调配统计', wzdptjData.value)
      }
    } else {
      console.error('物资调配统计数据格式不正确:', result)
    }
  } catch (error) {
    console.error('物资调配统计数据失败:', error)
  }
}

// 物资调配统计 - 右边
const wzdptjDataRight = ref(null)
const wzdptjtotal_timesRight = ref(null)
const wzdptjaverage_response_timeRight = ref(null)
const wzdptjDatatbRight = ref(null)

const getWzdptjDataRight = async () => {
  try {
    const result = await getWzdptjRight()
    // console.log('右侧物资调配统计', result)

    if (result) {
      if (result.code === 200) {
        wzdptjDataRight.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        wzdptjtotal_timesRight.value = wzdptjDataRight.value[0].total_times
        wzdptjaverage_response_timeRight.value = wzdptjDataRight.value[0].average_response_time
        wzdptjDatatbRight.value = wzdptjDataRight.value[0].data
        console.log('右侧物资调配统计', wzdptjDataRight.value)
      }
    } else {
      console.error('右侧物资调配统计数据格式不正确:', result)
    }
  } catch (error) {
    console.error('右侧物资调配统计数据失败:', error)
  }
}

// 人员调配统计 - 左边
const rywzData = ref(null)
const rywztotal_times = ref(null)
const rywzaverage_response_time = ref(null)
const rywzDatatb = ref(null)

const getRywzData = async () => {
  try {
    const result = await getRywz()
    // console.log('调度指令统计', result)

    if (result) {
      if (result.code === 200) {
        rywzData.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        rywztotal_times.value = rywzData.value[0].total_times
        rywzaverage_response_time.value = rywzData.value[0].average_response_time
        rywzDatatb.value = rywzData.value[0].data
        console.log('人员调配统计', rywzData.value)
      }
    } else {
      console.error('人员调配统计数据格式不正确:', result)
    }
  } catch (error) {
    console.error('人员调配统计数据失败:', error)
  }
}

// 人员调配统计 - 右边
const rywzDataRight = ref(null)
const rywztotal_timesRight = ref(null)
const rywzaverage_response_timeRight = ref(null)
const rywzDatatbRight = ref(null)

const getRywzDataRight = async () => {
  try {
    const result = await getRydptjRight()
    // console.log('右侧人员调配统计', result)

    if (result) {
      if (result.code === 200) {
        rywzDataRight.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        rywztotal_timesRight.value = rywzDataRight.value[0].total_times
        rywzaverage_response_timeRight.value = rywzDataRight.value[0].average_response_time
        rywzDatatbRight.value = rywzDataRight.value[0].data
        console.log('右侧人员调配统计', rywzDataRight.value)
      }
    } else {
      console.error('右侧人员调配统计数据格式不正确:', result)
    }
  } catch (error) {
    console.error('右侧人员调配统计数据失败:', error)
  }
}

// 排水设施调度统计 - 左边
const psssData = ref(null)
const getPsssData = async () => {
  try {
    const result = await getPsss()
    // console.log('调度指令统计', result)

    if (result) {
      if (result.code === 200) {
        const data = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        psssData.value = data[0].data
        console.log('排水设施调度统计', psssData.value)
      }
    } else {
      console.error('排水设施调度统计数据格式不正确:', result)
    }
  } catch (error) {
    console.error('排水设施调度统计数据失败:', error)
  }
}

// 排水设施调度统计 - 右边
const psssDataRight = ref(null)
const getPsssDataRight = async () => {
  try {
    const result = await getPsssddtjRight()
    // console.log('右侧排水设施调度统计', result)

    if (result) {
      if (result.code === 200) {
        const data = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        psssDataRight.value = data[0].data
        console.log('右侧排水设施调度统计', psssDataRight.value)
      }
    } else {
      console.error('右侧排水设施调度统计数据格式不正确:', result)
    }
  } catch (error) {
    console.error('右侧排水设施调度统计数据失败:', error)
  }
}

// 调度指令统计 - 左边
const ddzltjData = ref(null)
const total_adjustments = ref(null)
const forecast_adjustments = ref(null)
const rain_adjustments = ref(null)
const adjustment_types = ref(null)
const rain_adjustment_types = ref(null)
const getDdzltjData = async () => {
  try {
    const result = await getDdzltj()
    // console.log('调度指令统计', result)

    if (result) {
      if (result.code === 200) {
        ddzltjData.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        total_adjustments.value = ddzltjData.value[0].data.total_adjustments
        forecast_adjustments.value = ddzltjData.value[0].data.forecast_adjustments
        rain_adjustments.value = ddzltjData.value[0].data.rain_adjustments
        adjustment_types.value = ddzltjData.value[0].data.adjustment_types
        rain_adjustment_types.value = ddzltjData.value[0].data.rain_adjustment_types
        // console.log('调度指令统计', ddzltjData.value)

        // 数据加载完成后，重新初始化图表
        setTimeout(() => {
          if (dispatchChart1) {
            dispatchChart1.dispose()
          }
          if (dispatchChart2) {
            dispatchChart2.dispose()
          }
          initDispatchChart1()
          initDispatchChart2()
        }, 100)
      }
    } else {
      console.error('调度指令统计数据格式不正确:', result)
    }
  } catch (error) {
    console.error('调度指令统计数据失败:', error)
  }
}

// 调度指令统计 - 右边
const ddzltjDataRight = ref(null)
const total_adjustmentsRight = ref(null)
const forecast_adjustmentsRight = ref(null)
const rain_adjustmentsRight = ref(null)
const adjustment_typesRight = ref(null)
const rain_adjustment_typesRight = ref(null)
const getDdzltjDataRight = async () => {
  try {
    const result = await getDdzrtjRight()
    // console.log('右侧调度指令统计', result)

    if (result) {
      if (result.code === 200) {
        ddzltjDataRight.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        total_adjustmentsRight.value = ddzltjDataRight.value[0].data.total_adjustments
        forecast_adjustmentsRight.value = ddzltjDataRight.value[0].data.forecast_adjustments
        rain_adjustmentsRight.value = ddzltjDataRight.value[0].data.rain_adjustments
        adjustment_typesRight.value = ddzltjDataRight.value[0].data.adjustment_types
        rain_adjustment_typesRight.value = ddzltjDataRight.value[0].data.rain_adjustment_types
        console.log('右侧调度指令统计', ddzltjDataRight.value)

        // 数据加载完成后，重新初始化右侧图表
        setTimeout(() => {
          if (dispatchChart3) {
            dispatchChart3.dispose()
          }
          if (dispatchChart4) {
            dispatchChart4.dispose()
          }
          initDispatchChart3()
          initDispatchChart4()
        }, 100)
      }
    } else {
      console.error('右侧调度指令统计数据格式不正确:', result)
    }
  } catch (error) {
    console.error('右侧调度指令统计数据失败:', error)
  }
}

const dateData = ref(null)
const getDate = async () => {
  const result = await getDateData()
  if (result.code) {
    dateData.value = JSON.parse(result.data[0].value)
    changeDate()
  }
}
const holidays = ref(null)

// const holidays = [
//   '2025-07-01',
//   '2021-10-02',
//   '2021-10-03',
//   '2021-10-04',
//   '2021-10-05',
//   '2021-10-06',
//   '2021-10-07',
// ]

const isHoliday = ({ dayjs }) => {
  return holidays.value && holidays.value.some(item => item.date === dayjs.format('YYYY-MM-DD'))
}

const getPrecipitation = ({ dayjs }) => {
  if (!holidays.value) return null
  const item = holidays.value.find(item => item.date === dayjs.format('YYYY-MM-DD'))
  // 只有当 precipitation 存在且大于 0 时才返回值
  return item && item.precipitation && Number(item.precipitation) > 0 ? item.precipitation : null
}

// 检查日期是否有有效的降雨量（precipitation > 0）
const hasValidPrecipitation = ({ dayjs }) => {
  if (!holidays.value) return false
  const item = holidays.value.find(item => item.date === dayjs.format('YYYY-MM-DD'))
  return item && item.precipitation && Number(item.precipitation) > 0
}

// 检查日期是否应该被禁用（没有有效的降雨量）
const isDateDisabled = ({ dayjs }) => {
  return !hasValidPrecipitation({ dayjs })
}

// Element Plus 日期选择器的禁用日期函数
const disabledDate = date => {
  if (!holidays.value) return true

  // 使用本地时间格式化日期，避免时区问题
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const dateStr = `${year}-${month}-${day}`

  const item = holidays.value.find(item => item.date === dateStr)

  const isDisabled = !item || !item.precipitation || Number(item.precipitation) <= 0

  return isDisabled
}

const changeDate = () => {
  if (dateData.value && dateData.value.data) {
    // 将所有日期数据映射为统一格式，包含 date 和 precipitation 字段
    holidays.value = dateData.value.data
      .map(item => {
        // 尝试多种可能的字段名称
        const date = item.date || item.d || item.time || item.t
        const precipitation = item.precipitation || item.v || item.value || item.rain || 0

        const mappedItem = {
          date: date,
          precipitation: precipitation
        }
        return mappedItem
      })
      .filter(item => item.date) // 过滤掉没有日期的项

    // 特别检查 2025-07-03 的数据
    const july03 = holidays.value.find(item => item.date === '2025-07-03')

    // 检查所有有降雨量的日期
    const validDates = holidays.value.filter(item => Number(item.precipitation) > 0)

    // 强制刷新日期选择器（左侧和右侧）
    datePickerKey.value++
    datePickerKeyRight.value++
  }
}

// 辅助函数：安全地解析JSON，处理常见的格式问题
const safeJsonParse = jsonString => {
  try {
    // 如果已经是对象，直接返回
    if (typeof jsonString === 'object') return jsonString

    // 处理字符串
    if (typeof jsonString === 'string') {
      // 修复常见的JSON格式问题
      let valueStr = jsonString

      // 替换连续的逗号为单个逗号
      valueStr = valueStr.replace(/,\s*,/g, ',')

      // 替换数组末尾多余的逗号
      valueStr = valueStr.replace(/,\s*]/g, ']')

      // 替换对象末尾多余的逗号
      valueStr = valueStr.replace(/,\s*}/g, '}')

      return JSON.parse(valueStr)
    }

    return null
  } catch (e) {
    console.error('JSON解析失败:', e, jsonString)
    return null
  }
}

// 切换影响
const changeBlock = async item => {
  console.log(item, 'item111111111111111')
  const severity = item.severity?.slice(0, 2) // 截取前两位
  try {
    const result = await getAccumulatedFlood(severity) // 假设接口支持参数
    if (result) {
      if (result.code === 200) {
        accumulatedFlood.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        accumulatedFloodList.value = accumulatedFlood.value[0].data
        riskOverview.value = accumulatedFlood.value[0]
        console.log('积涝统计数据accumulatedFlood切换', accumulatedFlood.value)
      }
    } else {
      accumulatedFloodList.value = []
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    accumulatedFloodList.value = []
    console.error('获取预警数据失败:', error)
  }
}

// 切换影响
const changeBlockright = async item => {
  console.log(item, 'item111111111111111right')
  const severity = item.severity?.slice(0, 2) // 截取前两位
  try {
    const result = await getAccumulatedFloodRight(severity) // 假设接口支持参数
    if (result) {
      if (result.code === 200) {
        accumulatedFloodRight.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        accumulatedFloodRightList.value = accumulatedFloodRight.value[0].data
        riskOverviewRight.value = accumulatedFloodRight.value[0]
        console.log('积涝统计数据accumulatedFlood切换', accumulatedFloodRight.value)
      }
    } else {
      accumulatedFloodList.value = []
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    accumulatedFloodList.value = []
    console.error('获取预警数据失败:', error)
  }
}

const emit = defineEmits(['tagClicked', 'clickTable', 'yuntuClickShow', 'clickScene1', 'clickScene', 'clickDate','fangxunrenyuanClickemit','clickTableRight'])

// 单个点击
const handleClickTable = item => {
  console.log(item, '表格item')
  // 传递数据给父组件，父组件会将数据传递给RollupEffect组件
  emit('clickTable', { tableData: item })
}
const handleClickTableRight = item => {
  console.log(item, '表格item')
  emit('clickTableRight', { tableData: item })
}
// 右边
const handleTagClick2 = async item => {
  console.log(item, 'item')
  const result = await getLocation({ pageSize: 1000, sstype: item.type })
  console.log(result, 'result')

  // Emit the tag data and API result to the parent component
  emit('clickScene1', { tagData: item, locationData: result.rows })

  // 获取设备报警情况数据
  try {
    const deviceResult = await getDeviceTableRight(item.type)
    if (deviceResult && deviceResult.code === 200) {
      equipmentMaintenanceRight.value = deviceResult.data.map(item => safeJsonParse(item.value)).filter(Boolean)

      if (equipmentMaintenanceRight.value && equipmentMaintenanceRight.value.length > 0) {
        // 检查数据结构
        if (equipmentMaintenanceRight.value[0] && equipmentMaintenanceRight.value[0].data) {
          equipmentMaintenanceRightList.value = equipmentMaintenanceRight.value[0].data
        } else if (Array.isArray(equipmentMaintenanceRight.value)) {
          // 如果是数组结构，直接使用
          equipmentMaintenanceRightList.value = equipmentMaintenanceRight.value
        }
        console.log('设备报警情况数据:', equipmentMaintenanceRightList.value)
      }
    } else {
      console.error('获取设备报警情况数据格式不正确:', deviceResult)
    }
  } catch (error) {
    console.error('获取设备报警情况数据失败:', error)
  }
}

// 左边
const handleTagClick = async item => {
  console.log(item, 'item')
  const result = await getLocation({ pageSize: 1000, sstype: item.type })
  console.log(result, 'result')

  // Emit the tag data and API result to the parent component
  emit('clickScene', { tagData: item, locationData: result.rows })

  // 获取设备报警情况数据
  try {
    const deviceResult = await getDeviceTable(item.type)
    if (deviceResult && deviceResult.code === 200) {
      console.log(deviceResult, 'deviceResult')

      // 使用安全JSON解析函数处理数据
      equipmentAlarmDetail.value = deviceResult.data.map(item => safeJsonParse(item.value)).filter(Boolean)

      if (equipmentAlarmDetail.value && equipmentAlarmDetail.value.length > 0) {
        // 检查数据结构
        if (equipmentAlarmDetail.value[0] && equipmentAlarmDetail.value[0].data) {
          equipmentAlarmDetailList.value = equipmentAlarmDetail.value[0].data
        } else if (Array.isArray(equipmentAlarmDetail.value)) {
          // 如果是数组结构，直接使用
          equipmentAlarmDetailList.value = equipmentAlarmDetail.value
        }
        console.log('设备报警情况数据:', equipmentAlarmDetailList.value)
      }
    } else {
      console.error('获取设备报警情况数据格式不正确:', deviceResult)
    }
  } catch (error) {
    console.error('获取设备报警情况数据失败:', error)
  }
}

// 右边
// 设备报警情况
const equipmentMaintenanceRight = ref(null)
const equipmentMaintenanceRightList = ref(null)
const getEquipmentMaintenanceRightFc = async () => {
  try {
    const result = await getEquipmentMaintenanceRight()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        equipmentMaintenanceRight.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        equipmentMaintenanceRightList.value = equipmentMaintenanceRight.value[0].data
        // console.log('11111111111111112222222222:', equipmentAlarmDetail.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}
// 设备报警
const equipmentAlarmRight = ref(null)
const equipmentAlarmRightList = ref(null)
const getEquipmentAlarmRightFc = async () => {
  try {
    const result = await getEquipmentAlarmRight()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        equipmentAlarmRight.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        equipmentAlarmRightList.value = equipmentAlarmRight.value[0].data

        console.log('11111111111111112222222222:', equipmentAlarmRight.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 积涝统计
const accumulatedFloodRight = ref(null)
const accumulatedFloodRightList = ref(null)
const riskOverviewRight = ref(null)
// 联动
const getAccumulatedFloodRightFc = async () => {
  try {
    const result = await getAccumulatedFloodRight()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        accumulatedFloodRight.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        accumulatedFloodRightList.value = accumulatedFloodRight.value[0].data
        riskOverviewRight.value = accumulatedFloodRight.value[0]
        // console.log('11111111111111112222222222:', accumulatedFloodRight.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 物资调配统计
const materialAllocationRight = ref(null)
const materialAllocationRightList = ref(null)
const getMaterialAllocationRightFc = async () => {
  try {
    const result = await getMaterialAllocationRight()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        materialAllocationRight.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        materialAllocationRightList.value = materialAllocationRight.value[0].data
        // console.log('11111111111111112222222222:', materialAllocationRight.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 内涝影响分析
const impactofwaterloggingRight = ref(null)
const impactofwaterloggingRightList = ref(null)
const getImpactofwaterloggingRightFc = async () => {
  try {
    const result = await getImpactofwaterloggingRight()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        impactofwaterloggingRight.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        impactofwaterloggingRightList.value = impactofwaterloggingRight.value[0].data
        // console.log('11111111111111112222222222:', impactofwaterloggingRight.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}
// 人员调配统计
const personnelDeploymentRight = ref(null)
const personnelDeploymentRightList = ref(null)
const getPersonnelDeploymentRightFc = async () => {
  try {
    const result = await getPersonnelDeploymentRight()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        personnelDeploymentRight.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        personnelDeploymentRightList.value = personnelDeploymentRight.value[0].data
        // console.log('11111111111111112222222222:', personnelDeploymentRight.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 报警事件类型统计
const alarmeventRight = ref(null)
const alarmeventRightList = ref(null)
const getAlarmeventRightFc = async () => {
  try {
    const result = await getAlarmeventRight()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        alarmeventRight.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        alarmeventRightList.value = alarmeventRight.value[0].data
        // console.log('11111111111111112222222222:', alarmeventRight.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}
// 排水防涝统计/排水预警统计
const drainageDataRight = ref(null)
const drainageDataRightList = ref(null)
const rainfallRight = ref(null)
const getDrainageDataRightFc = async () => {
  try {
    const result = await getDrainageDataRight()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        drainageDataRight.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        rainfallRight.value = drainageDataRight.value[0].rainfall
        drainageDataRightList.value = drainageDataRight.value[0].flood_points
        // console.log('11111111111111112222222222:', drainageDataRight.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 左边
// 设备报警情况
const equipmentAlarmDetail = ref(null)
const equipmentAlarmDetailList = ref(null)
const getEquipmentAlarmDetailFc = async () => {
  try {
    const result = await getEquipmentMaintenance()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        equipmentAlarmDetail.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据

        // 检查数据结构
        if (equipmentAlarmDetail.value.length > 0) {
          if (equipmentAlarmDetail.value[0] && equipmentAlarmDetail.value[0].data) {
            equipmentAlarmDetailList.value = equipmentAlarmDetail.value[0].data
          } else if (Array.isArray(equipmentAlarmDetail.value)) {
            // 如果是数组结构，直接使用
            equipmentAlarmDetailList.value = equipmentAlarmDetail.value
          }
        }
        // console.log('11111111111111112222222222:', equipmentAlarmDetail.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 设备报警
const equipmentAlarm = ref(null)
const equipmentAlarmList = ref(null)
const getEquipmentAlarmFc = async () => {
  try {
    const result = await getEquipmentAlarm()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        equipmentAlarm.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        equipmentAlarmList.value = equipmentAlarm.value[0].data
        // console.log('11111111111111112222222222:', equipmentAlarm.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 积涝统计
const accumulatedFlood = ref(null)
const accumulatedFloodList = ref(null)
const riskOverview = ref(null)
const getAccumulatedFloodFc = async () => {
  try {
    const result = await getAccumulatedFlood()
    console.log('获取积涝数据成功:', result)
    if (result) {
      if (result.code === 200) {
        accumulatedFlood.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        accumulatedFloodList.value = accumulatedFlood.value[0].data
        riskOverview.value = accumulatedFlood.value[0]
        console.log('积涝统计数据accumulatedFlood', accumulatedFlood.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 物资调配统计
const materialAllocation = ref(null)
const materialAllocationList = ref(null)
const getMaterialAllocationFc = async () => {
  try {
    const result = await getMaterialAllocation()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        materialAllocation.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        materialAllocationList.value = materialAllocation.value[0].data
        // console.log('11111111111111112222222222:', materialAllocation.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}
// 内涝影响分析
const impactofwaterlogging = ref(null)
const impactofwaterloggingList = ref(null)
const getImpactofwaterloggingFc = async () => {
  try {
    const result = await getImpactofwaterlogging()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        impactofwaterlogging.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        impactofwaterloggingList.value = impactofwaterlogging.value[0].data
        // console.log('11111111111111112222222222:', impactofwaterlogging.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 人员调配统计
const personnelDeployment = ref(null)
const personnelDeploymentList = ref(null)
const getPersonnelDeploymentFc = async () => {
  try {
    const result = await getPersonnelDeployment()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        personnelDeployment.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean)
        // 过滤掉解析失败的数据
        personnelDeploymentList.value = personnelDeployment.value[0].data
        // console.log('111111111111111122222222222加载成功·实时:', personnelDeployment.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 报警事件类型统计
const alarmevent = ref(null)
const alarmeventList = ref(null)
const getAlarmeventFc = async () => {
  try {
    const result = await getAlarmevent()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        alarmevent.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        alarmeventList.value = alarmevent.value[0].data
        // console.log('111111111111111122222222222加载成功·实时:', alarmevent.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 排水防涝统计/排水预警统计
const drainageData = ref(null)
const drainageDataList = ref(null)
const rainfall = ref(null)
const getDrainageDataFc = async () => {
  try {
    const result = await getDrainageData()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        drainageData.value = result.data.map(item => safeJsonParse(item.value)).filter(Boolean) // 过滤掉解析失败的数据
        drainageDataList.value = drainageData.value[0].flood_points
        rainfall.value = drainageData.value[0].rainfall
        // console.log('111111111111111122222222222加载成功·实时:', drainageData.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}
const weaherData = ref({})
onMounted(async () => {
  cstqGET().then(res => {
    console.log(res, '天气', JSON.parse(res.data[0].value))
    if (res.code === 200 && res.data.length) {
      weaherData.value = JSON.parse(res.data[0].value)
    }
  })
  // 左边
  await getDate()
  await getDrainageDataFc()
  await getDdzltjData()
  await getPsssData()
  await getRywzData()
  await getBcddtjData()
  await getWzdptjData()
  await getyldbjtbData()
  await getjyjlData()

  // 右侧数据初始化
  await getyldbjtbDataRight()

  // await getAlarmeventFc()
  // await getPersonnelDeploymentFc()
  // await getImpactofwaterloggingFc()
  // await getMaterialAllocationFc()
  // await getAccumulatedFloodFc()
  // await getEquipmentAlarmFc()
  // await getEquipmentAlarmDetailFc()
  // await getLowAreaData() // 加载低洼地区数据

  // 右边
  await getDrainageDataRightFc()
  await getDdzltjDataRight()
  await getPsssDataRight()
  await getRywzDataRight()
  await getBcddtjDataRight()
  await getWzdptjDataRight()
  await getjyjlDataRight()
  // await getAlarmeventRightFc()
  // await getPersonnelDeploymentRightFc()
  // await getImpactofwaterloggingRightFc()
  // await getMaterialAllocationRightFc()
  // await getAccumulatedFloodRightFc()
  // await getEquipmentAlarmRightFc()
  // await getEquipmentMaintenanceRightFc()

  // 初始化审计类型统计图表
  initAuditChart()
  initAuditChart1()

  // 初始化调度指令统计图表
  setTimeout(() => {
    initDispatchChart1()
    initDispatchChart2()
    initDispatchChart3()
    initDispatchChart4()
  }, 100)
})

// 监听报警事件数据变化，更新图表
watch(
  alarmeventList,
  newVal => {
    if (newVal && auditChart) {
      auditChart.setOption({
        series: [
          {
            data: newVal
          }
        ]
      })
    }
  },
  { deep: true }
)

// 监听右侧报警事件数据变化，更新图表
watch(
  alarmeventRightList,
  newVal => {
    if (newVal && auditChart1) {
      auditChart1.setOption({
        series: [
          {
            data: newVal
          }
        ]
      })
    }
  },
  { deep: true }
)

// 获取assets静态资源
const getAssetsFile = url => {
  // 检查是否包含斜杠，如果包含则认为是带文件夹路径的
  if (url.includes('/')) {
    return new URL(`../../assets/images/${url}`, import.meta.url).href
  } else {
    // 不包含斜杠，默认从home文件夹获取
    return new URL(`../../assets/images/home/<USER>
  }
}
const getAssetsFileRight = url => {
  return new URL(`../../assets/images/assistantdecision/${url}`, import.meta.url).href
}

// 根据标题获取背景类名
const getBackgroundClass = title => {
  if (title === '轻度影响') {
    return 'bg-light'
  } else if (title === '中度影响') {
    return 'bg-medium'
  } else if (title === '重度影响') {
    return 'bg-heavy'
  }
  return 'bg-light' // 默认背景
}
// 时间和日期
const currentTime = ref('')
const currentDate = ref('')

// 日期选择器相关
const showDatePicker = ref(false)
const selectedDate = ref('')
const datePickerKey = ref(0) // 用于强制刷新日期选择器

// 右侧日期选择器相关
const showDatePickerRight = ref(false)
const selectedDateRight = ref('')
const datePickerKeyRight = ref(0) // 用于强制刷新右侧日期选择器

// 处理日期变化
const handleDateChange = async date => {
  selectedDate.value = date

  if (!date) return

  try {
    // 调用降雨日历相关接口，传入选择的日期
    await Promise.all([
      updateRainfallCalendarData(date)
      // updateRainfallCalendarDataRight(date)
    ])

    console.log('降雨日历数据更新完成')
  } catch (error) {
    console.error('更新降雨日历数据失败:', error)
  }
}

// 处理右侧日期变化
const handleDateChangeRight = async date => {
  selectedDateRight.value = date

  if (!date) return

  try {
    // 调用降雨日历相关接口，传入选择的日期，只更新右侧数据
    await updateRainfallCalendarDataRight(date)

    console.log('右侧降雨日历数据更新完成')
  } catch (error) {
    console.error('更新右侧降雨日历数据失败:', error)
  }
}

// 更新降雨日历数据 - 左侧
const updateRainfallCalendarData = async date => {
  try {
    console.log('开始更新降雨日历数据 - 左侧:', date)

    // 更新弹窗选择的日期
    yldbjSelectedDate.value = date

    // 1. 更新降雨积涝总览
    const jyjlResult = await getjyjltjDate(date)
    if (jyjlResult && jyjlResult.code === 200) {
      jyjlData.value = JSON.parse(jyjlResult.data[0].value)
      console.log('降雨日历-降雨积涝总览更新成功:', jyjlData.value)
    }

    // 2. 更新调度指令统计
    const ddzltjResult = await getddzltjDate(date)
    if (ddzltjResult && ddzltjResult.code === 200) {
      const data = ddzltjResult.data.map(item => safeJsonParse(item.value)).filter(Boolean)
      if (data.length > 0) {
        ddzltjData.value = data
        total_adjustments.value = data[0].data.total_adjustments
        forecast_adjustments.value = data[0].data.forecast_adjustments
        rain_adjustments.value = data[0].data.rain_adjustments
        adjustment_types.value = data[0].data.adjustment_types
        rain_adjustment_types.value = data[0].data.rain_adjustment_types
        console.log('降雨日历-调度指令统计更新成功:', data)

        // 重新初始化图表
        setTimeout(() => {
          if (dispatchChart1) dispatchChart1.dispose()
          if (dispatchChart2) dispatchChart2.dispose()
          initDispatchChart1()
          initDispatchChart2()
        }, 100)
      }
    }

    // 3. 更新人员调配统计
    const rydptjResult = await getrydptjDate(date)
    if (rydptjResult && rydptjResult.code === 200) {
      const data = rydptjResult.data.map(item => safeJsonParse(item.value)).filter(Boolean)
      if (data.length > 0) {
        rywzData.value = data
        rywztotal_times.value = data[0].total_times
        rywzaverage_response_time.value = data[0].average_response_time
        rywzDatatb.value = data[0].data
        console.log('降雨日历-人员调配统计更新成功:', data)
      }
    }

    // 4. 更新物资调配统计
    const wzdptjResult = await getwzdptjDate(date)
    if (wzdptjResult && wzdptjResult.code === 200) {
      const data = wzdptjResult.data.map(item => safeJsonParse(item.value)).filter(Boolean)
      if (data.length > 0) {
        wzdptjData.value = data
        wzdptjtotal_times.value = data[0].total_times
        wzdptjaverage_response_time.value = data[0].average_response_time
        wzdptjDatatb.value = data[0].data
        console.log('降雨日历-物资调配统计更新成功:', data)
      }
    }

    // 5. 更新移动泵车调配统计
    const bcdptjResult = await getydbcdptjDate(date)
    if (bcdptjResult && bcdptjResult.code === 200) {
      const data = bcdptjResult.data.map(item => safeJsonParse(item.value)).filter(Boolean)
      if (data.length > 0) {
        bcddtjData.value = data
        bcddtjtotal_times.value = data[0].total_times
        bcddtjaverage_response_time.value = data[0].average_response_time
        bcddtjDatatb.value = data[0].pumps
        console.log('降雨日历-移动泵车调配统计更新成功:', data)
      }
    }

    // 6. 更新排水设施调度统计
    const psssddtjResult = await getpsssddtjDate(date)
    if (psssddtjResult && psssddtjResult.code === 200) {
      const data = psssddtjResult.data.map(item => safeJsonParse(item.value)).filter(Boolean)
      if (data.length > 0) {
        psssData.value = data[0].data
        console.log('降雨日历-排水设施调度统计更新成功:', data)
      }
    }

    // 7. 更新降雨积涝总览表格
    const yldbjtbResult = await getYldbjtbDate(date, { pageNum: 1, pageSize: 20 })
    if (yldbjtbResult && yldbjtbResult.code === 200) {
      yldbjtbData.value = yldbjtbResult.rows
      if (yldbjtbResult) {
        emit('clickDate', { locationData: yldbjtbResult.rows })
      }
      console.log('降雨日历-降雨积涝总览表格更新成功:', yldbjtbData.value)
    }
  } catch (error) {
    console.error('更新降雨日历数据失败 - 左侧:', error)
  }
}

// 更新降雨日历数据 - 右侧
const updateRainfallCalendarDataRight = async date => {
  try {
    console.log('开始更新降雨日历数据 - 右侧:', date)

    // 更新弹窗选择的日期
    yldbjSelectedDate.value = date

    // 1. 更新降雨积涝总览
    const jyjlResult = await getjyjltjDate(date)
    if (jyjlResult && jyjlResult.code === 200) {
      jyjlDataRight.value = JSON.parse(jyjlResult.data[0].value)
      console.log('降雨日历-右侧降雨积涝总览更新成功:', jyjlDataRight.value)
    }

    // 2. 更新调度指令统计
    const ddzltjResult = await getddzltjDate(date)
    if (ddzltjResult && ddzltjResult.code === 200) {
      const data = ddzltjResult.data.map(item => safeJsonParse(item.value)).filter(Boolean)
      if (data.length > 0) {
        ddzltjDataRight.value = data
        total_adjustmentsRight.value = data[0].data.total_adjustments
        forecast_adjustmentsRight.value = data[0].data.forecast_adjustments
        rain_adjustmentsRight.value = data[0].data.rain_adjustments
        adjustment_typesRight.value = data[0].data.adjustment_types
        rain_adjustment_typesRight.value = data[0].data.rain_adjustment_types
        console.log('降雨日历-右侧调度指令统计更新成功:', data)

        // 重新初始化右侧图表
        setTimeout(() => {
          if (dispatchChart3) dispatchChart3.dispose()
          if (dispatchChart4) dispatchChart4.dispose()
          initDispatchChart3()
          initDispatchChart4()
        }, 100)
      }
    }

    // 3. 更新人员调配统计
    const rydptjResult = await getrydptjDate(date)
    if (rydptjResult && rydptjResult.code === 200) {
      const data = rydptjResult.data.map(item => safeJsonParse(item.value)).filter(Boolean)
      if (data.length > 0) {
        rywzDataRight.value = data
        rywztotal_timesRight.value = data[0].total_times
        rywzaverage_response_timeRight.value = data[0].average_response_time
        rywzDatatbRight.value = data[0].data
        console.log('降雨日历-右侧人员调配统计更新成功:', data)
      }
    }

    // 4. 更新物资调配统计
    const wzdptjResult = await getwzdptjDate(date)
    if (wzdptjResult && wzdptjResult.code === 200) {
      const data = wzdptjResult.data.map(item => safeJsonParse(item.value)).filter(Boolean)
      if (data.length > 0) {
        wzdptjDataRight.value = data
        wzdptjtotal_timesRight.value = data[0].total_times
        wzdptjaverage_response_timeRight.value = data[0].average_response_time
        wzdptjDatatbRight.value = data[0].data
        console.log('降雨日历-右侧物资调配统计更新成功:', data)
      }
    }

    // 5. 更新移动泵车调配统计
    const bcdptjResult = await getydbcdptjDate(date)
    if (bcdptjResult && bcdptjResult.code === 200) {
      const data = bcdptjResult.data.map(item => safeJsonParse(item.value)).filter(Boolean)
      if (data.length > 0) {
        bcddtjDataRight.value = data
        bcddtjtotal_timesRight.value = data[0].total_times
        bcddtjaverage_response_timeRight.value = data[0].average_response_time
        bcddtjDatatbRight.value = data[0].pumps
        console.log('降雨日历-右侧移动泵车调配统计更新成功:', data)
      }
    }

    // 6. 更新排水设施调度统计
    const psssddtjResult = await getpsssddtjDate(date)
    if (psssddtjResult && psssddtjResult.code === 200) {
      const data = psssddtjResult.data.map(item => safeJsonParse(item.value)).filter(Boolean)
      if (data.length > 0) {
        psssDataRight.value = data[0].data
        console.log('降雨日历-右侧排水设施调度统计更新成功:', data)
      }
    }

    // 7. 更新降雨积涝总览表格（右侧使用相同的表格数据）
    const yldbjtbResult = await getYldbjtbDate(date, { pageNum: 1, pageSize: 20 })
    if (yldbjtbResult && yldbjtbResult.code === 200) {
      yldbjtbData.value = yldbjtbResult.rows
      console.log('降雨日历-右侧降雨积涝总览表格更新成功:', yldbjtbData.value)
    }
  } catch (error) {
    console.error('更新降雨日历数据失败 - 右侧:', error)
  }
}

// 添加内涝积水点统计数据
const floodingStats = ref([
  { area: '央宣坪区', value: 3 },
  { area: '晋源区', value: 0 },
  { area: '杏花岭区', value: 7 },
  { area: '万柏林区', value: 5 },
  { area: '迎泽区', value: 6 },
  { area: '小店区', value: 5 }
])

// 人员调配统计数据
const personnelTableData = ref([
  { id: '02', name: '消防一大队', manager: '张xx', count: 4, phone: '123*254' },
  { id: '01', name: '消防一大队', manager: '王xx', count: 3, phone: '123*254' },
  { id: '03', name: '消防一大队', manager: '刘xx', count: 2, phone: '123*254' },
  { id: '04', name: '消防一大队', manager: '王xx', count: 1, phone: '123*125' },
  { id: '05', name: '消防一大队', manager: '张xx', count: 4, phone: '123*254' },
  { id: '06', name: '消防一大队', manager: '刘xx', count: 2, phone: '123*254' },
  { id: '06', name: '消防一大队', manager: '刘xx', count: 2, phone: '123*254' },
  { id: '06', name: '消防一大队', manager: '刘xx', count: 2, phone: '123*254' },
  { id: '07', name: '消防一大队', manager: '王xx', count: 3, phone: '123*254' }
])

// 内涝影响分析模块数据
const contentBlocks = ref([
  {
    title: '轻度',
    items: [
      { content: '道路', title: '道路' },
      { content: '积水点', title: '积水点' },
      { content: '人员', title: '人员' }
    ]
  },
  {
    title: '中度',
    items: [
      { content: '道路', title: '道路' },
      { content: '积水点', title: '积水点' },
      { content: '人员', title: '人员' }
    ]
  },
  {
    title: '重度',
    items: [
      { content: '道路', title: '道路' },
      { content: '积水点', title: '积水点' },
      { content: '人员', title: '人员' }
    ]
  }
])

// 积劳统计表格数据
const drainageTableData = ref([
  {
    id: '01',
    route: '低速线路1',
    timeSlot: '2025-05-01 08：00-2025-05-01 08：00',
    distance: '30公里',
    delayTime: '10',
    frequency: '20分钟'
  },
  {
    id: '02',
    route: '低速线路2',
    timeSlot: '2025-05-01 08：00-2025-05-01 08：00',
    distance: '30公里',
    delayTime: '10',
    frequency: '20分钟'
  },
  {
    id: '03',
    route: '低速线路3',
    timeSlot: '2025-05-01 08：00-2025-05-01 08：00',
    distance: '30公里',
    delayTime: '10',
    frequency: '20分钟'
  },
  {
    id: '01',
    route: '低速线路1',
    timeSlot: '2025-05-01 08：00-2025-05-01 08：00',
    distance: '30公里',
    delayTime: '10',
    frequency: '20分钟'
  },
  {
    id: '02',
    route: '低速线路2',
    timeSlot: '2025-05-01 08：00-2025-05-01 08：00',
    distance: '30公里',
    delayTime: '10',
    frequency: '20分钟'
  },
  {
    id: '03',
    route: '低速线路3',
    timeSlot: '2025-05-01 08：00-2025-05-01 08：00',
    distance: '30公里',
    delayTime: '10',
    frequency: '20分钟'
  },
  {
    id: '01',
    route: '低速线路1',
    timeSlot: '2025-05-01 08：00-2025-05-01 08：00',
    distance: '30公里',
    delayTime: '10',
    frequency: '20分钟'
  },
  {
    id: '01',
    route: '低速线路1',
    timeSlot: '2025-05-01 08：00-2025-05-01 08：00',
    distance: '30公里',
    delayTime: '10',
    frequency: '20分钟'
  },
  {
    id: '01',
    route: '低速线路1',
    timeSlot: '2025-05-01 08：00-2025-05-01 08：00',
    distance: '30公里',
    delayTime: '10',
    frequency: '20分钟'
  }
])

// 物资调配统计数据
const materialTableData = ref([
  { id: '01', name: '抽水泵', quantity: 2, manager: '王xx', phone: '123**254' },
  { id: '02', name: '抽水泵', quantity: 1, manager: '张xx', phone: '123**254' },
  { id: '03', name: '抽水泵', quantity: 3, manager: '林xx', phone: '123**254' },
  { id: '01', name: '抽水泵', quantity: 2, manager: '王xx', phone: '123**254' },
  { id: '02', name: '抽水泵', quantity: 1, manager: '张xx', phone: '123**254' },
  { id: '02', name: '抽水泵', quantity: 1, manager: '张xx', phone: '123**254' },
  { id: '02', name: '抽水泵', quantity: 1, manager: '张xx', phone: '123**254' },
  { id: '03', name: '抽水泵', quantity: 3, manager: '林xx', phone: '123**254' },
  { id: '03', name: '抽水泵', quantity: 3, manager: '林xx', phone: '123**254' }
])

// 设备报警数据
const equipmentAlarmData = ref([
  { name: '低洼地段', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '人行下穿', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '车辆下穿', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '棚户区', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '地下商超', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '密集区域', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '公共地下停车', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '涉河入口', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '雨水管网', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '污水处理厂', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '缓洪池', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '排水渠', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '雨污水泵站', icon: 'https://picsum.photos/100/100', status: '3/63/2' },
  { name: '泵车', icon: 'https://picsum.photos/100/100', status: '3/63/2' }
])

// 设备情况表格数据
const equipmentTableData = ref([
  {
    id: '01',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '02',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '02',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '02',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '02',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '02',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '02',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '01',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '02',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '01',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '02',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '01',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '01',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '02',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '01',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  },
  {
    id: '03',
    name: 'xx泵站',
    location: '晋源区xx',
    status: '优',
    inflow: '12',
    outflow: '12',
    operationStatus: '启动'
  }
])

// 审计类型统计数据 - 保留作为备用数据
const auditTypeData = [
  { value: 60, name: '设备故障' },
  { value: 40, name: '灾洪超标' },
  { value: 30, name: '官网拥堵' }
]

let timer = null
let auditChart = null
let auditChart1 = null

// 更新时间函数
const updateTime = () => {
  const now = new Date()

  // 格式化时分秒
  const hours = now.getHours().toString().padStart(2, '0')
  const minutes = now.getMinutes().toString().padStart(2, '0')
  const seconds = now.getSeconds().toString().padStart(2, '0')
  currentTime.value = `${hours}:${minutes}:${seconds}`

  // 格式化年月日
  const year = now.getFullYear()
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  currentDate.value = `${year}/${month}/${day}`
}

// 低洼地区数据
const lowAreaData = ref([
  {
    name: '低洼地段1',
    time: '2025-05-01 08：00',
    depth: '30',
    duration: '20分钟',
    alertLevel: 'Ⅰ',
    forecastLevel: 'Ⅰ'
  },
  {
    name: '低洼地段2',
    time: '2025-05-01 08：00',
    depth: '30',
    duration: '20分钟',
    alertLevel: 'Ⅱ',
    forecastLevel: 'Ⅱ'
  },
  {
    name: '低洼地段3',
    time: '2025-05-01 08：00',
    depth: '30',
    duration: '20分钟',
    alertLevel: 'Ⅲ',
    forecastLevel: 'Ⅲ'
  },
  { name: '低洼地段4', time: '2025-05-01 08：00', depth: '30', duration: '20分钟', alertLevel: 'Ⅳ', forecastLevel: 'Ⅳ' }
])

// 获取低洼地区数据函数
const getLowAreaData = async () => {
  try {
    // 这里可以添加API调用，暂时使用静态数据
    // const result = await getLowAreaList()
    // if (result && result.code === 200) {
    //   lowAreaData.value = result.data
    // }
    console.log('低洼地区数据加载成功')
  } catch (error) {
    console.error('获取低洼地区数据失败:', error)
  }
}

// 初始化审计类型统计图表
const initAuditChart = () => {
  // 由于已经替换为表格，不再需要初始化图表
  // 如果后续需要恢复图表，可以取消注释下面的代码
  /* 
  const chartDom = document.getElementById('audit-chart')
  if (!chartDom) return

  auditChart = echarts.init(chartDom)

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      show: true,
      orient: 'vertical',
      right: '5%',
      top: 'center',
      itemGap: 15,
      textStyle: {
        color: '#D8F1FF',
        fontSize: 18
      },
      itemWidth: 15,
      itemHeight: 15,
      itemStyle: {
        borderWidth: 0
      },
      formatter: name => {
        const item = alarmeventList.value ? alarmeventList.value.find(item => item.name === name) : null
        return `${name}  ${item ? item.value : ''}`
      }
    },
    color: ['#00A7FF', '#FFCC00', '#00CC99'],
    series: [
      {
        name: '报警事件类型',
        type: 'pie',
        right: '15%',
        radius: ['35%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}\n{c}',
          color: '#D8F1FF',
          fontSize: 18,
          fontWeight: 'normal',
          lineHeight: 18
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10,
          smooth: false,
          lineStyle: {
            color: '#D8F1FF'
          }
        },
        data: alarmeventList.value || auditTypeData
      }
    ]
  }

  auditChart.setOption(option)
  */
}

// 更新审计图表数据
const updateAuditChart = () => {
  if (!auditChart) return

  const option = {
    legend: {
      formatter: name => {
        const item = alarmeventList.value ? alarmeventList.value.find(item => item.name === name) : null
        return `${name}  ${item ? item.value : ''}`
      }
    },
    series: [
      {
        data: alarmeventList.value || auditTypeData
      }
    ]
  }

  auditChart.setOption(option)
}

// 更新右侧审计图表数据
const updateAuditChart1 = () => {
  if (!auditChart1) return

  const option = {
    legend: {
      formatter: name => {
        const item = alarmeventRightList.value ? alarmeventRightList.value.find(item => item.name === name) : null
        return `${name}  ${item ? item.value : ''}`
      }
    },
    series: [
      {
        data: alarmeventRightList.value || auditTypeData
      }
    ]
  }

  auditChart1.setOption(option)
}

// 初始化审计类型统计图表
const initAuditChart1 = () => {
  const chartDom = document.getElementById('audit-chart1')
  if (!chartDom) return

  auditChart1 = echarts.init(chartDom)

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      show: true,
      orient: 'vertical',
      right: '5%',
      top: 'center',
      itemGap: 15,
      textStyle: {
        color: '#D8F1FF',
        fontSize: 22
      },
      itemWidth: 15,
      itemHeight: 15,
      icon: 'stack',
      itemStyle: {
        borderWidth: 0,
        borderRadius: 0
      },
      formatter: name => {
        const item = alarmeventRightList.value ? alarmeventRightList.value.find(item => item.name === name) : null
        return `${name}  ${item ? item.value : ''}`
      }
    },
    color: ['#00A7FF', '#FFCC00', '#00CC99'],
    series: [
      {
        name: '报警事件类型',
        type: 'pie',
        right: '15%',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        // itemStyle: {
        //   borderRadius: 0,
        //   borderColor: '#042B52',
        //   borderWidth: 2
        // },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}\n{c}',
          color: '#D8F1FF',
          fontSize: 22,
          fontWeight: 'normal',
          lineHeight: 18
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10,
          smooth: false,
          lineStyle: {
            color: '#D8F1FF'
          }
        },
        data: alarmeventRightList.value || auditTypeData
      }
    ]
  }

  auditChart1.setOption(option)
}

// 调度指令统计图表变量
let dispatchTotalChart = null
let warningDispatchChart = null
let rainDispatchChart = null

// 初始化调度总数图表
const initDispatchTotalChart = () => {
  const chartDom = document.getElementById('dispatch-total-chart')
  if (!chartDom) return

  dispatchTotalChart = echarts.init(chartDom)

  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '10%',
      right: '10%',
      top: '10%',
      bottom: '20%'
    },
    xAxis: {
      type: 'category',
      data: ['全市', '民政', '建委', '平安保险'],
      axisLabel: {
        color: '#ffffff',
        fontSize: 10,
        rotate: 0
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      show: false,
      max: 150
    },
    series: [
      {
        type: 'bar',
        data: [120, 100, 130, 120],
        barWidth: '60%',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#00d4ff' },
            { offset: 1, color: '#0066cc' }
          ])
        }
      }
    ]
  }

  dispatchTotalChart.setOption(option)
}

// 初始化预警调度总数图表
const initWarningDispatchChart = () => {
  const chartDom = document.getElementById('warning-dispatch-chart')
  if (!chartDom) return

  warningDispatchChart = echarts.init(chartDom)

  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '10%',
      right: '10%',
      top: '10%',
      bottom: '20%'
    },
    xAxis: {
      type: 'category',
      data: ['全市', '民政', '建委', '平安保险'],
      axisLabel: {
        color: '#ffffff',
        fontSize: 10,
        rotate: 0
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      show: false,
      max: 150
    },
    series: [
      {
        type: 'bar',
        data: [90, 130, 130, 130],
        barWidth: '60%',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#00ff88' },
            { offset: 1, color: '#006644' }
          ])
        }
      }
    ]
  }

  warningDispatchChart.setOption(option)
}

// 初始化雨中调度总数图表
const initRainDispatchChart = () => {
  const chartDom = document.getElementById('rain-dispatch-chart')
  if (!chartDom) return

  rainDispatchChart = echarts.init(chartDom)

  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '10%',
      right: '10%',
      top: '10%',
      bottom: '20%'
    },
    xAxis: {
      type: 'category',
      data: ['雨量中心', '建委中心', '一二三四人员', '网格员'],
      axisLabel: {
        color: '#ffffff',
        fontSize: 10,
        rotate: 0
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      show: false,
      max: 150
    },
    series: [
      {
        type: 'bar',
        data: [90, 130, 130, 130],
        barWidth: '60%',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#ffaa00' },
            { offset: 1, color: '#cc6600' }
          ])
        }
      }
    ]
  }

  rainDispatchChart.setOption(option)
}

// 调度指令统计图表变量
let dispatchChart1 = null
let dispatchChart2 = null
let dispatchChart3 = null
let dispatchChart4 = null

// 初始化调度指令统计图表1
const initDispatchChart1 = () => {
  const chartDom = document.getElementById('dispatch-chart-1')
  if (!chartDom) {
    console.error('dispatch-chart-1 element not found')
    return
  }
  console.log('Initializing dispatch chart 1')
  dispatchChart1 = echarts.init(chartDom)

  // 使用动态数据源 adjustment_types
  const chartData = adjustment_types.value || []
  const categories = chartData.map(item => item.name || '未知')
  const values = chartData.map(item => item.value || 0)

  // 如果没有数据，使用默认数据
  const defaultCategories = ['全市', '民政', '建委', '平安保险']
  const defaultValues = [90, 120, 110, 100]

  const finalCategories = categories.length > 0 ? categories : defaultCategories
  const finalValues = values.length > 0 ? values : defaultValues

  // 动态计算最大值
  const maxValue = Math.max(...finalValues) * 1.2 || 150

  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '0%',
      right: '0%',
      top: '15%',
      bottom: '0%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: finalCategories,
      axisLabel: {
        color: '#ffffff',
        fontSize: 20,
        rotate: 0,
        interval: 0,
        margin: 10
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#999',
          type: 'dashed'
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      show: true,
      max: maxValue,
      min: 0,
      axisLabel: {
        color: '#ffffff',
        fontSize: 20
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#999',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        type: 'bar',
        data: finalValues,
        barWidth: '45%',
        itemStyle: {
          color: '#00A2EC',
          borderRadius: [2, 2, 0, 0]
        },
        label: {
          show: true,
          position: 'top',
          color: '#ffffff',
          fontSize: 20,
          fontWeight: 'normal',
          distance: 3
        }
      }
    ]
  }

  dispatchChart1.setOption(option)
}

// 初始化调度指令统计图表2
const initDispatchChart2 = () => {
  const chartDom = document.getElementById('dispatch-chart-2')
  if (!chartDom) {
    console.error('dispatch-chart-2 element not found')
    return
  }
  console.log('Initializing dispatch chart 2')
  dispatchChart2 = echarts.init(chartDom)

  // 使用动态数据源 rain_adjustment_types
  const chartData = rain_adjustment_types.value || []
  const categories = chartData.map(item => item.name || '未知')
  const values = chartData.map(item => item.value || 0)

  // 如果没有数据，使用默认数据
  const defaultCategories = ['时事中心', '警察中心', '一体人员', '网络']
  const defaultValues = [95, 130, 120, 110]

  const finalCategories = categories.length > 0 ? categories : defaultCategories
  const finalValues = values.length > 0 ? values : defaultValues

  // 动态计算最大值
  const maxValue = Math.max(...finalValues) * 1.2 || 150

  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '0%',
      right: '0%',
      top: '15%',
      bottom: '0%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: finalCategories,
      axisLabel: {
        color: '#ffffff',
        fontSize: 20,
        rotate: 0,
        interval: 0,
        margin: 10
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#999',
          type: 'dashed'
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      show: true,
      max: maxValue,
      min: 0,
      axisLabel: {
        color: '#ffffff',
        fontSize: 20
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#999',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        type: 'bar',
        data: finalValues,
        barWidth: '45%',
        itemStyle: {
          color: '#00A2EC',
          borderRadius: [2, 2, 0, 0]
        },
        label: {
          show: true,
          position: 'top',
          color: '#ffffff',
          fontSize: 20,
          fontWeight: 'normal',
          distance: 3
        }
      }
    ]
  }

  dispatchChart2.setOption(option)
}

// 初始化调度指令统计图表3 - 右侧
const initDispatchChart3 = () => {
  const chartDom = document.getElementById('dispatch-chart-3')
  if (!chartDom) {
    console.error('dispatch-chart-3 element not found')
    return
  }
  console.log('Initializing dispatch chart 3')
  dispatchChart3 = echarts.init(chartDom)

  // 使用动态数据源 adjustment_typesRight
  const chartData = adjustment_typesRight.value || []
  const categories = chartData.map(item => item.name)
  const values = chartData.map(item => item.value)

  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '0%',
      right: '0%',
      top: '15%',
      bottom: '0%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: categories.length > 0 ? categories : ['全市', '民政', '建委', '平安保险'],
      axisLabel: {
        color: '#ffffff',
        fontSize: 20,
        rotate: 0,
        interval: 0,
        margin: 10
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#999',
          type: 'dashed'
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      show: true,
      max: 150,
      min: 0,
      axisLabel: {
        color: '#ffffff',
        fontSize: 20
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#999',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        type: 'bar',
        data: values.length > 0 ? values : [90, 120, 110, 100],
        barWidth: '45%',
        itemStyle: {
          color: '#00A2EC',
          borderRadius: [2, 2, 0, 0]
        },
        label: {
          show: true,
          position: 'top',
          color: '#ffffff',
          fontSize: 20,
          fontWeight: 'normal',
          distance: 3
        }
      }
    ]
  }

  dispatchChart3.setOption(option)
}

// 初始化调度指令统计图表4 - 右侧
const initDispatchChart4 = () => {
  const chartDom = document.getElementById('dispatch-chart-4')
  if (!chartDom) {
    console.error('dispatch-chart-4 element not found')
    return
  }
  console.log('Initializing dispatch chart 4')
  dispatchChart4 = echarts.init(chartDom)

  // 使用动态数据源 rain_adjustment_typesRight
  const chartData = rain_adjustment_typesRight.value || []
  const categories = chartData.map(item => item.name)
  const values = chartData.map(item => item.value)

  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '0%',
      right: '0%',
      top: '15%',
      bottom: '0%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: categories.length > 0 ? categories : ['时事中心', '警察中心', '一体人员', '网络'],
      axisLabel: {
        color: '#ffffff',
        fontSize: 20,
        rotate: 0,
        interval: 0,
        margin: 10
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#999',
          type: 'dashed'
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      show: true,
      max: 150,
      min: 0,
      axisLabel: {
        color: '#ffffff',
        fontSize: 20
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#999',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        type: 'bar',
        data: values.length > 0 ? values : [95, 130, 120, 110],
        barWidth: '45%',
        itemStyle: {
          color: '#00A2EC',
          borderRadius: [2, 2, 0, 0]
        },
        label: {
          show: true,
          position: 'top',
          color: '#ffffff',
          fontSize: 20,
          fontWeight: 'normal',
          distance: 3
        }
      }
    ]
  }

  dispatchChart4.setOption(option)
}
// 连接数字孪生配置
// const options = ref({
//   domId: 'player', // 修改为新的ID
//   apiOptions: {
//     onReady: function () {
//       console.info('此时可以调API了')
//     }
//   }
// })

// 组件挂载时启动定时器和初始化屏幕适配
onMounted(async () => {
  updateTime() // 立即执行一次
  timer = setInterval(updateTime, 1000) // 每秒更新一次
  // 修改这里，接入地图配置

  // 监听窗口大小变化，重绘图表
  window.addEventListener('resize', () => {
    if (auditChart) {
      auditChart.resize()
    }
    if (auditChart1) {
      auditChart1.resize()
    }
    if (dispatchChart1) {
      dispatchChart1.resize()
    }
    if (dispatchChart2) {
      dispatchChart2.resize()
    }
  })
})

// 组件卸载时清除定时器和屏幕适配事件监听
onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }

  // 销毁DTS实例
  // if (api.value) {
  //   api.value = null
  // }

  // 销毁图表实例
  if (auditChart) {
    auditChart.dispose()
    auditChart = null
  }
  if (auditChart1) {
    auditChart1.dispose()
    auditChart1 = null
  }
  if (dispatchChart1) {
    dispatchChart1.dispose()
    dispatchChart1 = null
  }
  if (dispatchChart2) {
    dispatchChart2.dispose()
    dispatchChart2 = null
  }

  // 移除窗口大小变化监听
  window.removeEventListener('resize', () => {
    if (auditChart) {
      auditChart.resize()
    }
    if (auditChart1) {
      auditChart1.resize()
    }
  })
})
const yuntuClick = () => {
  emit('yuntuClickShow', 1)
}

// 存储当前选择的参数 - 左侧
const selectedCircleParam = ref(null)
const selectedLevelParam = ref(null)
const vetChartData = ref(null)

// 存储当前选择的参数 - 右侧
const selectedCircleParamRight = ref(null)
const selectedLevelParamRight = ref(null)
const vetChartDataRight = ref(null)
const yldbjtbDataRight = ref([])

// 处理圆形点击事件
const handleCircleClick = async param => {
  console.log('Circle clicked with param:', param)
  selectedCircleParam.value = param

  // 调用 getvetData 接口
  await updateVetData()
}

// 处理等级点击事件
const handleLevelClick = async param => {
  console.log('Level clicked with param:', param)
  selectedLevelParam.value = param

  // 调用 getvetData 接口
  await updateVetData()
}

// 更新 vetData 数据
const updateVetData = async () => {
  try {
    // 构建参数对象，根据接口要求传递 cate 和 grade 参数
    const params = {}

    // 将圆形参数映射到 cate
    if (selectedCircleParam.value !== null) {
      params.cate = selectedCircleParam.value
    }

    // 将等级参数映射到 grade
    if (selectedLevelParam.value !== null) {
      params.grade = selectedLevelParam.value
    }

    console.log('Calling getvetData with params:', params)

    // 调用 getvetData 接口
    const result = await getvetData(params)

    if (result && result.code === 200) {
      vetChartData.value = result.rows
      console.log('VetData updated successfully:', vetChartData.value)

      // 更新 chart-container 中的数据
      updateChartContainer()
    } else {
      console.error('获取 vetData 失败:', result)
    }
  } catch (error) {
    console.error('调用 getvetData 接口失败:', error)
  }
}

// 更新 chart-container 中的数据
const updateChartContainer = () => {
  try {
    if (vetChartData.value) {
      console.log('Updating chart container with vetChartData:', vetChartData.value)

      // 处理不同的数据结构
      let newTableData = []

      if (Array.isArray(vetChartData.value)) {
        // 如果是数组，直接使用
        newTableData = vetChartData.value
      } else if (vetChartData.value.rows) {
        // 如果有 rows 属性
        newTableData = vetChartData.value.rows
      } else if (vetChartData.value.data) {
        // 如果有 data 属性
        if (Array.isArray(vetChartData.value.data)) {
          newTableData = vetChartData.value.data
        } else {
          // 如果 data 是对象，尝试解析
          try {
            const parsedData = safeJsonParse(vetChartData.value.data)
            if (Array.isArray(parsedData)) {
              newTableData = parsedData
            } else if (parsedData && parsedData.rows) {
              newTableData = parsedData.rows
            }
          } catch (e) {
            console.error('解析 vetChartData.data 失败:', e)
          }
        }
      } else if (vetChartData.value.tableData) {
        // 如果有 tableData 属性
        newTableData = vetChartData.value.tableData
      }

      // 更新表格数据
      if (newTableData.length > 0) {
        yldbjtbData.value = newTableData
        console.log('Chart container updated successfully with data:', yldbjtbData.value)
      } else {
        console.log('No valid table data found in vetChartData')
      }
    }
  } catch (error) {
    console.error('更新 chart-container 失败:', error)
  }
}

// 右侧韦恩图相关函数
// 处理右侧圆形点击事件
const handleCircleClickRight = async param => {
  console.log('Right Circle clicked with param:', param)
  selectedCircleParamRight.value = param

  // 调用 getvetData 接口
  await updateVetDataRight()
}

// 处理右侧等级点击事件
const handleLevelClickRight = async param => {
  console.log('Right Level clicked with param:', param)
  selectedLevelParamRight.value = param

  // 调用 getvetData 接口
  await updateVetDataRight()
}

// 更新右侧 vetData 数据
const updateVetDataRight = async () => {
  try {
    // 构建参数对象，根据接口要求传递 cate 和 grade 参数
    const params = {}

    // 将圆形参数映射到 cate
    if (selectedCircleParamRight.value !== null) {
      params.cate = selectedCircleParamRight.value
    }

    // 将等级参数映射到 grade
    if (selectedLevelParamRight.value !== null) {
      params.grade = selectedLevelParamRight.value
    }

    console.log('Calling getvetData for right side with params:', params)

    // 调用 getvetData 接口
    const result = await getvetData(params)

    if (result && result.code === 200) {
      vetChartDataRight.value = result.rows
      console.log('Right VetData updated successfully:', vetChartDataRight.value)

      // 更新右侧 chart-container 中的数据
      updateChartContainerRight()
    } else {
      console.error('获取右侧 vetData 失败:', result)
    }
  } catch (error) {
    console.error('调用右侧 getvetData 接口失败:', error)
  }
}

// 更新右侧 chart-container 中的数据
const updateChartContainerRight = () => {
  try {
    if (vetChartDataRight.value) {
      console.log('Updating right chart container with vetChartData:', vetChartDataRight.value)

      // 处理不同的数据结构
      let newTableData = []

      if (Array.isArray(vetChartDataRight.value)) {
        // 如果是数组，直接使用
        newTableData = vetChartDataRight.value
      } else if (vetChartDataRight.value.rows) {
        // 如果有 rows 属性
        newTableData = vetChartDataRight.value.rows
      } else if (vetChartDataRight.value.data) {
        // 如果有 data 属性
        if (Array.isArray(vetChartDataRight.value.data)) {
          newTableData = vetChartDataRight.value.data
        } else {
          // 如果 data 是对象，尝试解析
          try {
            const parsedData = safeJsonParse(vetChartDataRight.value.data)
            if (Array.isArray(parsedData)) {
              newTableData = parsedData
            } else if (parsedData && parsedData.rows) {
              newTableData = parsedData.rows
            }
          } catch (e) {
            console.error('解析右侧 vetChartData.data 失败:', e)
          }
        }
      } else if (vetChartDataRight.value.tableData) {
        // 如果有 tableData 属性
        newTableData = vetChartDataRight.value.tableData
      }

      // 更新右侧表格数据
      if (newTableData.length > 0) {
        yldbjtbDataRight.value = newTableData
        console.log('Right chart container updated successfully with data:', yldbjtbDataRight.value)
      } else {
        console.log('No valid table data found in right vetChartData')
      }
    }
  } catch (error) {
    console.error('更新右侧 chart-container 失败:', error)
  }
}
</script>

<style lang="scss" scoped>
.cell {
  padding: 5px 0;
  box-sizing: border-box;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  //  width: 80px;
  //  height: 80px;
  .text {
    font-size: 16px;
    font-weight: 500;
    line-height: 1.2;
  }
}

// 添加低洼地区表格样式
.low-area-table {
  width: 100%;
  height: 100%;
  overflow: hidden;

  table {
    width: 100%;
    border-collapse: collapse;
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-size: 26px;

    th {
      background: #0d4873;
      padding: vh(15) vw(5);
      text-align: center;
      font-size: 26px;
      font-weight: normal;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    td {
      padding: vh(12) vw(5);
      text-align: center;
      font-size: 26px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      white-space: nowrap;
    }

    thead {
      display: table-header-group;
    }

    tbody {
      display: block;
      height: vh(500);
      // background-color: rgba(64, 209, 44, 0.2);
      overflow-y: auto;

      /* 隐藏滚动条 */
      &::-webkit-scrollbar {
        width: 0;
        display: none;
      }
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE and Edge */

      tr {
        display: table;
        width: 100%;
        table-layout: fixed;

        &:nth-child(odd) {
          background-color: rgba(13, 72, 115, 0.2);
        }
        &:hover {
          background-color: rgba(13, 72, 115, 0.5);
        }
      }
    }

    thead tr {
      display: table;
      width: 100%;
      table-layout: fixed;
    }
  }
}

.cell.has-rain {
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 4px;
}

.cell.disabled {
  color: #c0c4cc !important;
  background-color: #f5f7fa !important;
  cursor: not-allowed !important;

  .text {
    color: #c0c4cc !important;
    font-size: 16px;
    font-weight: 500;
  }

  .precipitation {
    display: none !important;
  }
}

// .cell .holiday {
//   position: absolute;
//   width: 6px;
//   height: 6px;
//   background: var(--el-color-danger);
//   border-radius: 50%;
//   bottom: 0px;
//   left: 50%;
//   transform: translateX(-50%);
// }

.cell .precipitation {
  font-size: 12px;
  color: #409eff;
  font-weight: bold;
  line-height: 1;
  margin-top: 2px;
}

// 日期选择器整体样式优化
:deep(.el-date-picker) {
  .el-picker-panel__body {
    .el-date-table {
      td {
        width: 80px;
        height: 80px;

        .cell {
          height: 80px;
          width: 80px;

          font-size: 16px;
          font-weight: 500;
        }
      }
    }
  }
}
// Global module header styles
.module-header,
.equipment-header,
.top-title-area,
.bottom-title-area,
.drainage-title {
  background-image: url('@/assets/images/home/<USER>') !important;
  background-size: 100% 100% !important;
  background-repeat: no-repeat !important;
  padding: 0 vh(12) 0 vw(45) !important;
  border-radius: 0 !important;
}

// Remove border-radius from all content containers
.module-item-1,
.module-item-2,
.module-item-3,
.second-module-1,
.second-module-2,
.second-module-3,
.module-2-top,
.module-2-middle,
.module-2-bottom,
.module-3-top,
.equipment-container,
.content-item,
.stats-card,
.grid-item,
.chart-container,
.personnel-table-container,
.drainage-table-container,
.material-table-container,
.equipment-table-container,
.equipment-filter,
.drainage-title,
.filter-input {
  border-radius: 0 !important;
}

.module-title,
.equipment-title,
.area-title,
.drainage-title,
.title-text {
  font-size: vh(26) !important;
  color: #d8f1ff !important;
  font-family: JiangChengXieHei !important;
  font-weight: normal !important;
}

.module-time {
  color: #d8f1ff !important;
  cursor: pointer;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

// 屏幕容器
.screen-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
  pointer-events: none; // 设置为none，让鼠标事件穿透到地图
}

// 背景层
.bg-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  pointer-events: none;
  z-index: 1;
}

.bg-layer-1 {
  background-image: url('@/assets/images/home/<USER>');
  z-index: 1;
}

.bg-layer-2 {
  background-image: url('@/assets/images/home/<USER>');
  z-index: 2;
}

.bg-layer-3 {
  background-image: url('@/assets/images/home/<USER>');
  z-index: 3;
}

// 地图容器样式
.map-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: auto;
}

// 拆分为三个独立的容器
.left-container,
.middle-container,
.right-container {
  position: absolute;
  top: 0;
  height: 100%;
  z-index: 10;
  pointer-events: none;
}

.left-container {
  left: 0;
  width: 32%;
  height: 100%;
  padding: vh(40) vw(0) vh(40) vw(100);
  // background: rgb(150, 214, 153);
  // background: linear-gradient(90deg, rgba(0, 0, 0, 0) 0%, rgba(19, 44, 57, 0.7745) 15%, rgba(19, 44, 57, 0.95) 100%);
  .left-section {
    width: 100%;
    height: 100%;
    pointer-events: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .time-weather {
    display: flex;
    align-items: center;
    color: #fff;
    padding-left: vw(90);
    // margin-bottom: vh(5);
    // background: blue;
    .time-date {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
    }

    .time {
      font-size: vh(24);
    }

    .date {
      font-size: vh(14);
      margin-right: vw(2);
    }

    .divider {
      margin: 0 vw(20);
      width: vw(1);
      height: vh(30);
      background-color: #3a607c;
    }

    .weather {
      display: flex;
      align-items: center;
    }

    .weather-icon {
      width: vw(24);
      height: vh(24);
      margin-right: vw(10);
    }

    .weather-icon1 {
      width: vw(24);
      height: vh(22);
    }

    .temperature {
      font-size: vh(24);
    }
  }
  .content-layout-first {
    flex: 1;
    width: 100%;
    // background: rgba(0, 0, 0, 0.2);
    .content-layout-header {
      width: 100%;
      height: vh(120);
      background-image: url('@/assets/images/weather-bg.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: center;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 vw(95);

      .weather-info-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 100%;
        color: #fff;
        padding: 0 vw(15);
      }

      .current-weather {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 100%;
        gap: vw(48);
      }

      .weather-label {
        font-weight: normal;
        font-size: vh(24);
        color: #ffffff;
        line-height: vh(33);
      }

      .city-info {
        display: flex;
        align-items: center;
      }

      .location-icon {
        display: inline-block;
        width: vw(22);
        height: vh(24);
        margin-right: vw(20);
      }

      .city-name {
        font-weight: normal;
        font-size: vh(32);
        color: #ffffff;
        margin-top: vh(-8);
      }

      .weather-detail {
        display: flex;
        align-items: flex-start;
        flex-direction: column;
        justify-content: center;
        height: 100%;
        gap: vw(15);
        min-width: 400px;
        font-size: 23px;
      }

      .weather-icon-large {
        display: flex;
        align-items: center;
      }

      .weather-icon-large img {
        width: vw(24);
        height: vh(24);
        margin-right: vw(10);
        object-fit: contain;
      }

      .weather-data {
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .weather-type {
        display: flex;
        align-items: center;
        gap: vw(10);
      }

      .weather-name {
        font-weight: normal;
        font-size: vh(30);
        color: #ffffff;
        line-height: vh(42);
        text-align: center;
        font-style: normal;
      }

      .weather-temp {
        font-weight: normal;
        font-size: vh(22);
        color: #ffffff;
        line-height: vh(30);
        font-style: normal;
        text-align: center;
        width: vw(72);
        height: vh(31);
        background: #1ecfa5;
        border-radius: vw(16);
      }

      .wind-info {
        display: flex;
        align-items: center;
        gap: vw(20);
        font-weight: normal;
        font-size: vh(27);
        color: #ffffff;
        text-align: left;
        font-style: normal;
        margin-top: vh(3);
      }

      .weather-forecast {
        display: flex;
        height: 100%;
        align-items: center;
      }

      .forecast-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0 vw(30);
      }

      .forecast-title {
        font-weight: normal;
        font-size: vh(20);
        color: #ffffff;
        text-align: left;
        font-style: normal;
      }

      .forecast-value {
        .forecast-value-num {
          font-weight: bold;
          font-size: vh(33);
          color: #24ceb8;
          font-style: normal;
        }

        .forecast-value-unit {
          font-weight: normal;
          font-size: vh(24);
          color: #ffffff;
          font-style: normal;
        }
      }

      .forecast-value1 {
        .forecast-value-num {
          font-weight: bold;
          font-size: vh(24);
          color: #92d3ec;
          font-style: normal;
        }

        .forecast-value-unit {
          font-weight: normal;
          font-size: vh(24);
          color: #ffffff;
          font-style: normal;
        }
      }

      .publish-info {
        width: vw(395);
        height: vh(77);
        background: rgba(110, 204, 255, 0.14);
        border-radius: vw(1);
        display: flex;
        justify-content: center;
        align-items: center;
        gap: vw(20);
      }

      .publish-label {
        font-weight: normal;
        font-size: vh(25);
        color: #ffffff;
        font-style: normal;
      }

      .publish-times {
        font-weight: normal;
        font-size: vh(22);
        color: #ffffff;
        font-style: normal;
      }

      .time-item {
        font-weight: normal;
        font-size: vh(22);
        color: #ffffff;
        font-style: normal;
      }

      .divider-line {
        width: vw(1);
        height: vh(70);
      }
    }
  }
  .left-content {
    // background: red;
    // background: rgba(255, 255, 255, 0.24);
    width: 100%;
    height: calc(100% - vh(180));
    // background: red;

    // 设置所有表格字号为24
    table {
      font-size: 24px;

      // 设置tbody滚动并隐藏滚动条
      tbody {
        display: block;
        overflow-y: auto;

        /* 隐藏滚动条 */
        &::-webkit-scrollbar {
          width: 0;
          display: none;
        }
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */

        tr {
          display: table;
          width: 100%;
          table-layout: fixed;
        }
        tr,
        td {
          cursor: pointer;
        }
      }

      thead {
        display: table-header-group;

        tr {
          display: table;
          width: 100%;
          table-layout: fixed;
        }
      }
    }

    .content-layout-second {
      width: 100%;
      height: 100%;
      display: flex;
      gap: vw(20);

      .second-module-1 {
        width: 37%;
        height: 100%;
        border-radius: vh(4);
        display: flex;
        flex-direction: column;
        // gap: vh(20);

        .module-item-1 {
          height: 40%;
          width: 100%;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          border-radius: vh(4);
          display: flex;
          flex-direction: column;
        }

        .module-header {
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: flex-start;
          margin-bottom: vh(10);
          // background: rgba(6, 72, 146, 0.5);
          padding: vh(8) vh(12);
          border-radius: vh(4);
          position: relative;

          .module-title {
            font-weight: normal;
            font-size: vh(28);
            color: #ffffff;
            font-style: normal;
          }

          .module-time {
            font-weight: normal;
            font-size: vh(22);
            color: #ffffff;
            font-style: normal;
            cursor: pointer;
            display: flex;
            align-items: center;
          }

          .date-picker-container {
            position: absolute;
            // top: 100%;
            left: 140px;
            border: none !important;
            z-index: 1000;
            // margin-top: vh(5);

            :deep(.el-input__wrapper) {
              background-color: rgba(6, 72, 146, 0.8);
              // border: 1px solid #409eff;
              border: none !important;

              .el-input__inner {
                color: #ffffff;

                &::placeholder {
                  color: rgba(255, 255, 255, 0.6);
                }
              }
            }

            :deep(.el-date-editor) {
              background-color: rgba(6, 72, 146, 0.8);
            }
          }
        }

        .module-content {
          display: flex;
          gap: vh(10);
          padding: vh(0) vw(10);
          height: 100%;
          width: 100%;
          justify-content: center;

          .stats-row {
            display: flex;
            justify-content: space-between;
            gap: vw(10);
            width: 100%;
            height: 100;
          }
          .stats-col-1 {
            width: 30%;
            height: 100%;
            display: flex;
            flex-direction: column;
            gap: vh(40);
          }
          .stats-col-2 {
            width: 70%;
            height: 100%;
            display: flex;
            flex-direction: column;
            gap: vh(10);
          }

          .stats-label {
            margin-top: 10px;
            font-weight: 400;
            font-size: vh(24);
            height: vh(45);
            width: 100%;
            color: #ffffff;
            font-style: normal;
            background-image: url('@/assets/images/home/<USER>');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: left;
            padding: 0 vw(35);
            display: flex;
            justify-content: space-between;
            align-items: center;
          }

          .stats-card {
            display: flex;
            align-items: center;
            width: 100%;
            height: 100%;
            gap: vw(10);
            border-radius: vh(4);
            padding: vh(10);
            margin-top: 10px;
            height: vh(95); // 添加固定高度
            box-sizing: border-box; // 确保padding不会增加元素总高度
          }

          .stats-icon {
            width: vw(90);
            height: vh(90);
            img {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }
          }

          .stats-data {
            display: flex;
            flex-direction: column;
          }

          .stats-value {
            font-weight: 700;
            font-size: vh(28);
            color: #fff;
            font-style: normal;
          }

          .stats-unit {
            font-weight: normal;
            font-size: vh(24);
            font-weight: 350;
            color: #ffffff;
            font-style: normal;
          }

          // .stats-grid {
          //   display: grid;
          //   grid-template-columns: repeat(3, 1fr);
          //   gap: vw(10);
          // }

          /* 韦恩图容器样式 */
          .venn-diagram-container {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            // padding: vh(20);
          }

          .venn-diagram {
            position: relative;
          }

          .circle:hover {
            scale: 1.1;
            z-index: 5;
            cursor: pointer;
          }
          /* 三个圆形的基础样式 */
          .circle {
            position: absolute;
            width: vw(200);
            height: vh(200);
            border-radius: 50%;
          }

          /* 第一个圆形 - 左上 */
          .circle-1 {
            top: vh(-60);
            left: vw(-40);
            border: 15px solid #00d4ff;
          }

          /* 第二个圆形 - 右上 */
          .circle-2 {
            top: vh(-60);
            right: vw(-40);
            border: 15px solid #1ecfa5;
          }

          /* 第三个圆形 - 下方 */
          .circle-3 {
            bottom: vh(-50);
            left: 45%;
            transform: translateX(-50%);
            border: 15px solid #d8e93d;
          }

          /* 圆形标签样式 */
          .circle-label {
            position: absolute;
            font-size: vh(24);
            color: #ffffff;
            white-space: nowrap;
            font-weight: 400;
            text-align: center;
          }

          .circle-label.top-left {
            bottom: vh(-42);
            left: vw(30);
            width: vw(180);
          }

          .circle-label.top-right {
            bottom: vh(-42);
            right: vw(80);
            width: vw(180);
          }

          .circle-label.bottom {
            top: vh(-50);
            left: 50%;
            transform: translateX(-50%);
            width: vw(180);
          }

          /* 重叠区域数字样式 */
          .overlap-number {
            position: absolute;
            font-size: vh(24);
            font-weight: bold;
            color: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
          }

          /* 左侧重叠区域 */
          .overlap-left {
            top: vh(-30);
            left: vw(40);
          }

          /* 右侧重叠区域 */
          .overlap-right {
            top: vh(-30);
            right: vw(40);
          }

          /* 底部重叠区域 */
          .overlap-bottom {
            bottom: vh(-30);
            left: 50%;
            transform: translateX(-50%);
          }

          /* 中心重叠区域 */
          .overlap-center {
            top: vh(55);
            left: 50%;
            transform: translateX(-50%);
          }

          .grid-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: vh(4);
            height: vh(95); // 添加与stats-card相同的高度
            box-sizing: border-box; // 确保padding不会增加元素总高度
            transition: all 0.3s ease;
            cursor: pointer;
          }

          .grid-value {
            font-weight: 700;
            font-size: vh(36);
            color: #fff;
            font-style: normal;
            background-image: url('@/assets/images/assistantdecision/nlbg.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: bottom;
            height: vh(68);
            width: 100%;
            display: flex;
            align-items: flex-start;
            justify-content: center;
          }

          .grid-label {
            font-weight: 400;
            font-size: vh(20);
            color: #ffffff;
            font-style: normal;
          }
        }

        .module-item-2 {
          height: 60%;
          width: 100%;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          border-radius: vh(4);
          .module-header {
            width: 100%;
            display: flex;
            justify-content: flex-end;
            align-items: flex-end;
            // background: rgba(214, 212, 62, 0.5);
            padding: vh(8) vh(12);
            border-radius: vh(4);
            background-image: none !important;

            .module-title {
              font-weight: normal;
              font-size: vh(28);
              color: #ffffff;
              background: none;
              font-style: normal;
            }
          }
          .color-level {
            width: 100%;
            height: 20px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            gap: vw(15);
            .level-item {
              width: 34px;
              height: 10px;
              cursor: pointer;
            }
            .level-1 {
              background: #0f40f5;
            }
            .level-2 {
              background: #ffbf6b;
            }
            .level-3 {
              background: #fff81d;
            }
            .level-4 {
              background: #bd3124;
            }
          }
          .chart-container {
            width: 100%;
            height: calc(100% - vh(60));
            display: flex;
            justify-content: center;
            align-items: center;
            padding: vh(10);
            // background: rgba(255, 255, 255, 0.05);
            border-radius: vh(4);

            .chart {
              width: 100%;
              height: 100%;
              box-sizing: border-box;
            }
          }
        }
      }

      .second-module-2 {
        width: 63%;
        height: 100%;
        border-radius: vh(4);
        display: flex;
        flex-direction: column;
        gap: vh(20);
        overflow: hidden; /* 添加overflow:hidden防止内容溢出 */

        .module-2-top {
          height: 50%;
          width: 100%;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: vh(10);
          overflow: hidden; /* 添加overflow:hidden防止内容溢出 */
          min-height: 0; /* 确保flex子项可以收缩 */

          .top-title-area {
            height: vh(45);
            width: 100%;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            // background: rgba(6, 72, 146, 0.5);
            padding: vh(8) vh(12);
            border-radius: vh(4);

            .area-title {
              font-weight: normal;
              font-size: vh(24);
              color: #ffffff;
              font-style: normal;
            }
          }

          .top-content-area {
            width: 100%;
            height: calc(100% - vh(45));
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            gap: vh(10);

            .content-block {
              flex: 1;
              height: 100%;
              border-radius: vh(4);
              padding: vh(10);
              background: rgba(27, 105, 190, 0.24);
              display: flex;
              flex-direction: column;
              gap: vh(5);

              .block-title {
                font-weight: 400;
                font-size: vh(23);
                height: vh(30);
                margin-bottom: vh(10);
                color: #ffffff;
                font-style: normal;
                background-image: url('@/assets/images/home/<USER>');
                background-size: cover;
                background-repeat: no-repeat;
                background-position: left;
                padding: 0 vw(35);
                display: flex;
                justify-content: space-between;
                align-items: center;

                .title-text {
                  font-weight: normal;
                  font-size: vh(18);
                  color: #ffffff;
                  font-style: normal;
                }
              }

              .block-content {
                flex: 1;
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                gap: vh(5);

                .content-item {
                  flex: 1;

                  border-radius: vh(4);
                  padding: vh(5);
                  font-weight: normal;
                  font-size: vh(16);
                  color: #ffffff;
                  font-style: normal;
                  text-align: center;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;

                  .content-item-value {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;
                    height: 100%;

                    .content-item-value-text {
                      width: vw(82);
                      height: vh(82);
                      background-size: cover;
                      background-repeat: no-repeat;
                      background-position: center;
                      margin-bottom: vh(5);
                      display: flex;
                      justify-content: center;
                      align-items: center;
                      font-size: vh(35);
                      font-weight: 700;
                      color: #ffffff;
                      &.bg-light {
                        background-image: url('@/assets/images/assistantdecision/qdbg.png');
                      }

                      &.bg-medium {
                        background-image: url('@/assets/images/assistantdecision/zdbg.png');
                      }

                      &.bg-heavy {
                        background-image: url('@/assets/images/assistantdecision/zhgbg.png');
                      }
                    }
                  }
                  .content-item-title-box {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;
                    height: 100%;
                  }

                  .content-item-title {
                    font-size: vh(23);
                    color: #ffffff;
                    width: vw(82);
                  }
                }
              }
            }
          }
          .module-2-top-left {
            width: 50%;
            height: 100%;
            display: flex;
            flex-direction: column;

            .top-title-area {
              height: vh(40);
              display: flex;
              align-items: center;

              .area-title {
                font-size: vh(24);
                color: #ffffff;
                font-weight: bold;
              }
            }

            .charts-container {
              flex: 1;
              display: flex;
              justify-content: space-between;
              align-items: center;
              gap: vw(10);
              padding: vh(10) 0;
              .charts-container-left {
                width: 25%;
                height: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                gap: vh(50);
              }
              .charts-container-right {
                width: 75%;
                height: 100%;
                display: flex;
                flex-direction: column;
                gap: vh(5);

                .chart-wrapper {
                  width: 100%;
                  height: 50%;
                  padding: vh(5);

                  .dispatch-chart {
                    width: 100%;
                    height: 100%;
                    min-height: 140px;
                  }
                }
              }

              .chart-item {
                // flex: 1;
                // height: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;

                .chart-title {
                  font-size: vh(28);
                  color: #ffffff;
                  margin-bottom: vh(5);
                  text-align: center;
                }

                .chart-subtitle {
                  font-size: vh(28);
                  color: #00d4ff;
                  font-weight: bold;
                  margin-bottom: vh(10);
                  text-align: center;
                }

                .mini-chart {
                  flex: 1;
                  width: 100%;
                  min-height: vh(120);
                }
              }
            }
          }
          .module-2-top-right {
            width: 50%;
            height: 100%;
            .top-title-area {
              display: flex;
              justify-content: space-between;
              align-items: center;
              .title-tip {
                color: #fff;
                font-size: vh(20);
              }
            }
            .module-2-top-right-tb {
              width: 100%;
              height: calc(100% - vh(40));
              overflow: hidden;

              table {
                border-collapse: collapse;
                width: 100%;
                margin: 20px auto;
                table-layout: fixed; /* 固定表格布局，确保列宽一致 */
                font-size: 24px;
              }

              th,
              td {
                border: 2px solid rgba(255, 255, 255, 0.4);
                padding: 10px;
                color: #ffffff;
                text-align: center;
                // width: 12.5%; /* 8列，每列宽度为1/8 */
                word-wrap: break-word; /* 防止内容溢出 */
                font-size: 24px;
              }

              th {
                font-weight: bold;
              }

              thead {
                display: table-header-group;
              }

              tbody {
                display: block;
                height: vh(250);

                overflow-y: auto;

                /* 隐藏滚动条 */
                &::-webkit-scrollbar {
                  width: 0;
                  display: none;
                }
                scrollbar-width: none; /* Firefox */
                -ms-overflow-style: none; /* IE and Edge */

                tr {
                  display: table;
                  width: 100%;
                  table-layout: fixed;
                }
              }

              thead tr {
                display: table;
                width: 100%;
                table-layout: fixed;
              }

              /* 表头层次样式区分 */
              .level-1 {
                // background-color: #e6f2ff;
                font-size: 24px;
              }
              .level-2 {
                // background-color: #f0f7ff;
                font-size: 24px;
              }
              .level-3 {
                // background-color: #f7fbff;
                font-size: 24px;
              }
            }
          }
        }

        .module-2-bottom {
          height: 50%;
          width: 100%;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          border-radius: vh(4);
          display: flex;
          flex-direction: column;
          padding: vh(10);
          gap: vh(10);
          overflow: hidden; /* 添加overflow:hidden防止内容溢出 */
          min-height: 0; /* 确保flex子项可以收缩 */

          .bottom-title-area {
            height: vh(45);
            width: 100%;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            // background: rgba(6, 72, 146, 0.7);
            padding: vh(8) vh(12);

            .area-title {
              font-weight: normal;
              font-size: vh(24);
              color: #ffffff;
              font-style: normal;
            }
          }

          .bottom-content-area {
            flex: 1;
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: stretch;
            overflow: hidden; /* 添加overflow:hidden防止内容溢出 */
            gap: vh(10); /* 添加间距 */

            /* 三个均分的部分样式 */
            .personnel-section,
            .material-section,
            .pump-section {
              flex: 1;
              display: flex;
              flex-direction: column;
              background: rgba(255, 255, 255, 0.05);
              border-radius: vh(4);
              padding: vh(10);
              overflow: hidden;

              .section-header {
                margin-bottom: vh(10);

                .section-title {
                  color: #ffffff;
                  font-size: vh(24);
                  font-weight: bold;
                  text-align: center;
                  margin: 0;
                }
              }

              .section-table {
                flex: 1;
                overflow: hidden;

                table {
                  width: 100%;
                  border-collapse: collapse;
                  color: #ffffff;
                  table-layout: fixed;
                  border-spacing: 0;
                  height: 100%;
                  font-size: 26px;

                  th {
                    background: #0d4873;
                    padding: vh(8) vw(3);
                    text-align: center;
                    font-size: 26px;
                    font-weight: normal;
                    border: none;
                    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                    position: sticky;
                    top: 0;
                    z-index: 1;
                  }

                  td {
                    padding: vh(8) vw(3);
                    text-align: center;
                    font-size: 26px;
                    border: none;
                    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  }

                  tbody {
                    display: block;
                    height: vh(370);
                    // background-color: rgba(64, 209, 44, 0.2);
                    overflow-y: auto;

                    /* 隐藏滚动条 */
                    &::-webkit-scrollbar {
                      width: 0;
                      display: none;
                    }
                    scrollbar-width: none; /* Firefox */
                    -ms-overflow-style: none; /* IE and Edge */
                  }

                  thead,
                  tbody tr {
                    display: table;
                    width: 100%;
                    table-layout: fixed;
                  }
                }
              }
            }

            .material-table-container {
              width: 100%;
              height: 100%;
              background: rgba(255, 255, 255, 0.05);
              border-radius: vh(4);
              padding: vh(10);
              display: flex;
              flex-direction: column;
              overflow: hidden; /* 添加overflow:hidden防止内容溢出 */

              .material-table {
                width: 100%;
                border-collapse: collapse;
                color: #ffffff;
                table-layout: fixed;
                border-spacing: 0;
                font-size: 24px;

                th {
                  background: #0d4873;
                  padding: vh(10) vw(5);
                  text-align: center;
                  font-size: 24px;
                  font-weight: normal;
                  border: none;
                  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                  position: sticky;
                  top: 0;
                  z-index: 1;
                }

                td {
                  padding: vh(10) vw(5);
                  text-align: center;
                  font-size: 24px;
                  border: none;
                  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  max-width: vw(100);
                }

                tbody {
                  display: block;
                  max-height: vh(280);
                  overflow-y: auto;

                  /* 隐藏滚动条 */
                  &::-webkit-scrollbar {
                    width: 0;
                    display: none;
                  }
                  scrollbar-width: none; /* Firefox */
                  -ms-overflow-style: none; /* IE and Edge */
                }

                thead,
                tbody tr {
                  display: table;
                  width: 100%;
                  table-layout: fixed;
                }
              }
            }
          }
        }
      }
    }
  }
}

.middle-container {
  left: 46%;
  height: auto;
  .middle-section {
    /* 中间区域标题 */
    .section-title {
      font-family: YouSheBiaoTiHei;
      font-size: vh(80);
      color: #ffffff;
      text-align: center;
      font-style: normal;
    }

    /* 导航按钮 */
    .nav-buttons {
      display: flex;
      justify-content: center;
      gap: vw(20);
      margin-top: vh(60);
    }

    .nav-button {
      width: vw(200);
      height: vh(80);
      background-image: url('@/assets/images/home/<USER>');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      display: flex;
      justify-content: center;
      padding-top: vh(14);
      font-family: JiangChengXieHei;
      color: #fff;
      font-size: vh(24);
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .nav-button.active {
      background-image: url('@/assets/images/home/<USER>');
      color: #fff;
      font-weight: bold;
    }
  }
}

.right-container {
  left: 68%;
  width: 32%;
  padding: vh(40) vw(100) vh(40) vw(0);
  .right-section {
    width: 100%;
    height: 100%;

    pointer-events: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .user-portal-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: vh(10) vw(20);
    gap: vw(30);
    border-radius: vh(4);
    // background: rgba(6, 72, 146, 0.24); // 添加背景色

    .user-info,
    .portal-back {
      display: flex;
      align-items: center;
      gap: vw(10);

      img {
        width: vh(40);
        height: vh(40);
        border-radius: 50%;
        object-fit: cover;
      }

      span {
        color: #fff;
        font-size: vh(16);
      }
    }
  }

  .right-content {
    width: 100%;
    height: 96%;
    table {
      font-size: 24px;

      // 设置tbody滚动并隐藏滚动条
      tbody {
        display: block;
        overflow-y: auto;

        /* 隐藏滚动条 */
        &::-webkit-scrollbar {
          width: 0;
          display: none;
        }
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */

        tr {
          display: table;
          width: 100%;
          table-layout: fixed;
        }
        tr,
        td {
          cursor: pointer;
        }
      }

      thead {
        display: table-header-group;

        tr {
          display: table;
          width: 100%;
          table-layout: fixed;
        }
      }
    }

    .content-layout-second {
      width: 100%;
      height: 100%;
      display: flex;
      gap: vw(20);

      .second-module-1 {
        width: 37%;
        height: 100%;
        border-radius: vh(4);
        display: flex;
        flex-direction: column;
        // gap: vh(20);

        .module-item-1 {
          height: 40%;
          width: 100%;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          border-radius: vh(4);
          display: flex;
          flex-direction: column;
        }

        .module-header {
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: flex-start;
          margin-bottom: vh(10);
          // background: rgba(6, 72, 146, 0.5);
          padding: vh(8) vh(12);
          border-radius: vh(4);
          position: relative;

          .module-title {
            font-weight: normal;
            font-size: vh(28);
            color: #ffffff;
            font-style: normal;
          }

          .module-time {
            font-weight: normal;
            font-size: vh(22);
            color: #ffffff;
            font-style: normal;
            cursor: pointer;
            display: flex;
            align-items: center;
          }

          .date-picker-container {
            position: absolute;
            // top: 100%;
            left: 140px;
            border: none !important;
            z-index: 1000;
            // margin-top: vh(5);

            :deep(.el-input__wrapper) {
              background-color: rgba(6, 72, 146, 0.8);
              // border: 1px solid #409eff;
              border: none !important;

              .el-input__inner {
                color: #ffffff;

                &::placeholder {
                  color: rgba(255, 255, 255, 0.6);
                }
              }
            }

            :deep(.el-date-editor) {
              background-color: rgba(6, 72, 146, 0.8);
            }
          }
        }

        .module-content {
          display: flex;
          gap: vh(10);
          padding: vh(0) vw(10);
          height: 100%;
          width: 100%;
          justify-content: center;

          .stats-row {
            display: flex;
            justify-content: space-between;
            gap: vw(10);
            width: 100%;
            height: 100;
          }
          .stats-col-1 {
            width: 30%;
            height: 100%;
            display: flex;
            flex-direction: column;
            gap: vh(40);
          }
          .stats-col-2 {
            width: 70%;
            height: 100%;
            display: flex;
            flex-direction: column;
            gap: vh(10);
          }

          .stats-label {
            margin-top: 10px;
            font-weight: 400;
            font-size: vh(24);
            height: vh(45);
            width: 100%;
            color: #ffffff;
            font-style: normal;
            background-image: url('@/assets/images/home/<USER>');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: left;
            padding: 0 vw(35);
            display: flex;
            justify-content: space-between;
            align-items: center;
          }

          .stats-card {
            display: flex;
            align-items: center;
            width: 100%;
            height: 100%;
            gap: vw(10);
            border-radius: vh(4);
            padding: vh(10);
            margin-top: 10px;
            height: vh(95); // 添加固定高度
            box-sizing: border-box; // 确保padding不会增加元素总高度
          }

          .stats-icon {
            width: vw(90);
            height: vh(90);
            img {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }
          }

          .stats-data {
            display: flex;
            flex-direction: column;
          }

          .stats-value {
            font-weight: 700;
            font-size: vh(28);
            color: #fff;
            font-style: normal;
          }

          .stats-unit {
            font-weight: normal;
            font-size: vh(24);
            font-weight: 350;
            color: #ffffff;
            font-style: normal;
          }

          // .stats-grid {
          //   display: grid;
          //   grid-template-columns: repeat(3, 1fr);
          //   gap: vw(10);
          // }

          /* 韦恩图容器样式 */
          .venn-diagram-container {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            // padding: vh(20);
          }

          .venn-diagram {
            position: relative;
          }

          /* 三个圆形的基础样式 */
          .circle {
            position: absolute;
            width: vw(230);
            height: vh(230);
            border-radius: 50%;
          }
          .circle:hover {
            scale: 1.1;
            z-index: 5;
            cursor: pointer;
          }
          /* 第一个圆形 - 左上 */
          .circle-1 {
            top: vh(-40);
            left: vw(-40);
            border: 15px solid #00d4ff;
          }

          /* 第二个圆形 - 右上 */
          .circle-2 {
            top: vh(-30);
            right: vw(-40);
            border: 15px solid #1ecfa5;
          }

          /* 第三个圆形 - 下方 */
          .circle-3 {
            bottom: vh(-80);
            left: 45%;
            transform: translateX(-50%);
            border: 15px solid #d8e93d;
          }

          /* 圆形标签样式 */
          .circle-label {
            position: absolute;
            font-size: vh(24);
            color: #ffffff;
            white-space: nowrap;
            font-weight: 400;
            text-align: center;
          }

          .circle-label.top-left {
            bottom: vh(-42);
            left: vw(30);
            width: vw(180);
          }

          .circle-label.top-right {
            bottom: vh(-42);
            right: vw(80);
            width: vw(180);
          }

          .circle-label.bottom {
            top: vh(-50);
            left: 50%;
            transform: translateX(-50%);
            width: vw(180);
          }

          /* 重叠区域数字样式 */
          .overlap-number {
            position: absolute;
            font-size: vh(24);
            font-weight: bold;
            color: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
          }

          /* 左侧重叠区域 */
          .overlap-left {
            top: vh(0);
            left: vw(40);
          }

          /* 右侧重叠区域 */
          .overlap-right {
            top: vh(0);
            right: vw(40);
          }

          /* 底部重叠区域 */
          .overlap-bottom {
            bottom: vh(-60);
            left: 50%;
            transform: translateX(-50%);
          }

          /* 中心重叠区域 */
          .overlap-center {
            top: vh(80);
            left: 50%;
            transform: translateX(-50%);
          }

          .grid-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: vh(4);
            height: vh(95); // 添加与stats-card相同的高度
            box-sizing: border-box; // 确保padding不会增加元素总高度
            transition: all 0.3s ease;
            cursor: pointer;
          }

          .grid-value {
            font-weight: 700;
            font-size: vh(36);
            color: #fff;
            font-style: normal;
            background-image: url('@/assets/images/assistantdecision/nlbg.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: bottom;
            height: vh(68);
            width: 100%;
            display: flex;
            align-items: flex-start;
            justify-content: center;
          }

          .grid-label {
            font-weight: 400;
            font-size: vh(20);
            color: #ffffff;
            font-style: normal;
          }
        }

        .module-item-2 {
          height: 60%;
          width: 100%;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          border-radius: vh(4);
          .module-header {
            width: 100%;
            display: flex;
            justify-content: flex-end;
            align-items: flex-end;
            // background: rgba(6, 72, 146, 0.5);
            padding: vh(8) vh(12);
            border-radius: vh(4);
            background-image: none !important;

            .module-title {
              font-weight: normal;
              font-size: vh(28);
              color: #ffffff;
              background: none;
              font-style: normal;
            }
          }
          .color-level {
            width: 100%;
            height: 20px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            gap: vw(15);
            .level-item {
              width: 34px;
              height: 10px;
              cursor: pointer;
            }
            .level-1 {
              background: #0f40f5;
            }
            .level-2 {
              background: #ffbf6b;
            }
            .level-3 {
              background: #fff81d;
            }
            .level-4 {
              background: #bd3124;
            }
          }
          .chart-container {
            width: 100%;
            height: calc(100% - vh(60));
            display: flex;
            justify-content: center;
            align-items: center;
            padding: vh(10);
            // background: rgba(255, 255, 255, 0.05);
            border-radius: vh(4);
            .low-area-table {
              width: 100%;
              height: 100%;
              overflow: hidden;

              table {
                width: 100%;
                border-collapse: collapse;
                color: #ffffff;
                border: 1px solid rgba(255, 255, 255, 0.2);
                font-size: 26px;

                th {
                  background: #0d4873;
                  padding: vh(15) vw(5);
                  text-align: center;
                  font-size: 26px;
                  font-weight: normal;
                  border: 1px solid rgba(255, 255, 255, 0.2);
                }

                td {
                  padding: vh(12) vw(5);
                  text-align: center;
                  font-size: 26px;
                  border: 1px solid rgba(255, 255, 255, 0.2);
                  white-space: nowrap;
                }

                thead {
                  display: table-header-group;
                }

                tbody {
                  display: block;
                  height: vh(572);

                  overflow-y: auto;

                  /* 隐藏滚动条 */
                  &::-webkit-scrollbar {
                    width: 0;
                    display: none;
                  }
                  scrollbar-width: none; /* Firefox */
                  -ms-overflow-style: none; /* IE and Edge */

                  tr {
                    display: table;
                    width: 100%;
                    table-layout: fixed;

                    &:nth-child(odd) {
                      background-color: rgba(13, 72, 115, 0.2);
                    }
                    &:hover {
                      background-color: rgba(13, 72, 115, 0.5);
                    }
                  }
                }

                thead tr {
                  display: table;
                  width: 100%;
                  table-layout: fixed;
                }
              }
            }
            .chart {
              width: 100%;
              height: 100%;
              box-sizing: border-box;
            }
          }
        }
      }

      .second-module-2 {
        width: 63%;
        height: 100%;
        border-radius: vh(4);
        display: flex;
        flex-direction: column;
        gap: vh(20);
        overflow: hidden; /* 添加overflow:hidden防止内容溢出 */

        .module-2-top {
          height: 50%;
          width: 100%;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: vh(10);
          overflow: hidden; /* 添加overflow:hidden防止内容溢出 */
          min-height: 0; /* 确保flex子项可以收缩 */

          .top-title-area {
            height: vh(45);
            width: 100%;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            // background: rgba(6, 72, 146, 0.5);
            padding: vh(8) vh(12);
            border-radius: vh(4);

            .area-title {
              font-weight: normal;
              font-size: vh(24);
              color: #ffffff;
              font-style: normal;
            }
          }

          .top-content-area {
            width: 100%;
            height: calc(100% - vh(45));
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            gap: vh(10);

            .content-block {
              flex: 1;
              height: 100%;
              border-radius: vh(4);
              padding: vh(10);
              background: rgba(27, 105, 190, 0.24);
              display: flex;
              flex-direction: column;
              gap: vh(5);

              .block-title {
                font-weight: 400;
                font-size: vh(23);
                height: vh(30);
                margin-bottom: vh(10);
                color: #ffffff;
                font-style: normal;
                background-image: url('@/assets/images/home/<USER>');
                background-size: cover;
                background-repeat: no-repeat;
                background-position: left;
                padding: 0 vw(35);
                display: flex;
                justify-content: space-between;
                align-items: center;

                .title-text {
                  font-weight: normal;
                  font-size: vh(18);
                  color: #ffffff;
                  font-style: normal;
                }
              }

              .block-content {
                flex: 1;
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                gap: vh(5);

                .content-item {
                  flex: 1;

                  border-radius: vh(4);
                  padding: vh(5);
                  font-weight: normal;
                  font-size: vh(16);
                  color: #ffffff;
                  font-style: normal;
                  text-align: center;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;

                  .content-item-value {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;
                    height: 100%;

                    .content-item-value-text {
                      width: vw(82);
                      height: vh(82);
                      background-size: cover;
                      background-repeat: no-repeat;
                      background-position: center;
                      margin-bottom: vh(5);
                      display: flex;
                      justify-content: center;
                      align-items: center;
                      font-size: vh(35);
                      font-weight: 700;
                      color: #ffffff;
                      &.bg-light {
                        background-image: url('@/assets/images/assistantdecision/qdbg.png');
                      }

                      &.bg-medium {
                        background-image: url('@/assets/images/assistantdecision/zdbg.png');
                      }

                      &.bg-heavy {
                        background-image: url('@/assets/images/assistantdecision/zhgbg.png');
                      }
                    }
                  }
                  .content-item-title-box {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;
                    height: 100%;
                  }

                  .content-item-title {
                    font-size: vh(23);
                    color: #ffffff;
                    width: vw(82);
                  }
                }
              }
            }
          }
          .module-2-top-left {
            width: 50%;
            height: 100%;
            display: flex;
            flex-direction: column;

            .top-title-area {
              height: vh(40);
              display: flex;
              align-items: center;

              .area-title {
                font-size: vh(24);
                color: #ffffff;
                font-weight: bold;
              }
            }

            .charts-container {
              flex: 1;
              display: flex;
              justify-content: space-between;
              align-items: center;
              gap: vw(10);
              padding: vh(10) 0;
              .charts-container-left {
                width: 25%;
                height: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                gap: vh(50);
              }
              .charts-container-right {
                width: 75%;
                height: 100%;
                display: flex;
                flex-direction: column;
                gap: vh(5);

                .chart-wrapper {
                  width: 100%;
                  height: 50%;
                  padding: vh(5);

                  .dispatch-chart {
                    width: 100%;
                    height: 100%;
                    min-height: 140px;
                  }
                }
              }

              .chart-item {
                // flex: 1;
                // height: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;

                .chart-title {
                  font-size: vh(28);
                  color: #ffffff;
                  margin-bottom: vh(5);
                  text-align: center;
                }

                .chart-subtitle {
                  font-size: vh(28);
                  color: #00d4ff;
                  font-weight: bold;
                  margin-bottom: vh(10);
                  text-align: center;
                }

                .mini-chart {
                  flex: 1;
                  width: 100%;
                  min-height: vh(120);
                }
              }
            }
          }
          .module-2-top-right {
            width: 50%;
            height: 100%;
            .top-title-area {
              display: flex;
              justify-content: space-between;
              align-items: center;
              .title-tip {
                color: #fff;
                font-size: vh(20);
              }
            }
            .module-2-top-right-tb {
              width: 100%;
              height: calc(100% - vh(40));
              overflow: hidden;

              table {
                border-collapse: collapse;
                width: 100%;
                margin: 20px auto;
                table-layout: fixed; /* 固定表格布局，确保列宽一致 */
                font-size: 24px;
              }

              th,
              td {
                border: 2px solid rgba(255, 255, 255, 0.4);
                padding: 10px;
                color: #ffffff;
                text-align: center;
                // width: 12.5%; /* 8列，每列宽度为1/8 */
                word-wrap: break-word; /* 防止内容溢出 */
                font-size: 24px;
              }

              th {
                font-weight: bold;
              }

              thead {
                display: table-header-group;
              }

              tbody {
                display: block;
                height: vh(290);

                overflow-y: auto;

                /* 隐藏滚动条 */
                &::-webkit-scrollbar {
                  width: 0;
                  display: none;
                }
                scrollbar-width: none; /* Firefox */
                -ms-overflow-style: none; /* IE and Edge */

                tr {
                  display: table;
                  width: 100%;
                  table-layout: fixed;
                }
              }

              thead tr {
                display: table;
                width: 100%;
                table-layout: fixed;
              }

              /* 表头层次样式区分 */
              .level-1 {
                // background-color: #e6f2ff;
                font-size: 24px;
              }
              .level-2 {
                // background-color: #f0f7ff;
                font-size: 24px;
              }
              .level-3 {
                // background-color: #f7fbff;
                font-size: 24px;
              }
            }
          }
        }

        .module-2-bottom {
          height: 50%;
          width: 100%;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          border-radius: vh(4);
          display: flex;
          flex-direction: column;
          padding: vh(10);
          gap: vh(10);
          overflow: hidden; /* 添加overflow:hidden防止内容溢出 */
          min-height: 0; /* 确保flex子项可以收缩 */

          .bottom-title-area {
            height: vh(45);
            width: 100%;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            // background: rgba(6, 72, 146, 0.7);
            padding: vh(8) vh(12);

            .area-title {
              font-weight: normal;
              font-size: vh(24);
              color: #ffffff;
              font-style: normal;
            }
          }

          .bottom-content-area {
            flex: 1;
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: stretch;
            overflow: hidden; /* 添加overflow:hidden防止内容溢出 */
            gap: vh(10); /* 添加间距 */

            /* 三个均分的部分样式 */
            .personnel-section,
            .material-section,
            .pump-section {
              flex: 1;
              display: flex;
              flex-direction: column;
              background: rgba(255, 255, 255, 0.05);
              border-radius: vh(4);
              padding: vh(10);
              overflow: hidden;

              .section-header {
                margin-bottom: vh(10);

                .section-title {
                  color: #ffffff;
                  font-size: vh(24);
                  font-weight: bold;
                  text-align: center;
                  margin: 0;
                }
              }

              .section-table {
                flex: 1;
                overflow: hidden;

                table {
                  width: 100%;
                  border-collapse: collapse;
                  color: #ffffff;
                  table-layout: fixed;
                  border-spacing: 0;
                  height: 100%;
                  font-size: 26px;

                  th {
                    background: #0d4873;
                    padding: vh(8) vw(3);
                    text-align: center;
                    font-size: 26px;
                    font-weight: normal;
                    border: none;
                    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                    position: sticky;
                    top: 0;
                    z-index: 1;
                  }

                  td {
                    padding: vh(8) vw(3);
                    text-align: center;
                    font-size: 26px;
                    border: none;
                    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  }

                  tbody {
                    display: block;
                    height: vh(430);
                    // background-color: rgba(64, 209, 44, 0.2);
                    overflow-y: auto;

                    /* 隐藏滚动条 */
                    &::-webkit-scrollbar {
                      width: 0;
                      display: none;
                    }
                    scrollbar-width: none; /* Firefox */
                    -ms-overflow-style: none; /* IE and Edge */
                  }

                  thead,
                  tbody tr {
                    display: table;
                    width: 100%;
                    table-layout: fixed;
                  }
                }
              }
            }

            .material-table-container {
              width: 100%;
              height: 100%;
              background: rgba(255, 255, 255, 0.05);
              border-radius: vh(4);
              padding: vh(10);
              display: flex;
              flex-direction: column;
              overflow: hidden; /* 添加overflow:hidden防止内容溢出 */

              .material-table {
                width: 100%;
                border-collapse: collapse;
                color: #ffffff;
                table-layout: fixed;
                border-spacing: 0;
                font-size: 24px;

                th {
                  background: #0d4873;
                  padding: vh(10) vw(5);
                  text-align: center;
                  font-size: 24px;
                  font-weight: normal;
                  border: none;
                  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                  position: sticky;
                  top: 0;
                  z-index: 1;
                }

                td {
                  padding: vh(10) vw(5);
                  text-align: center;
                  font-size: 24px;
                  border: none;
                  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  max-width: vw(100);
                }

                tbody {
                  display: block;
                  max-height: vh(280);
                  overflow-y: auto;

                  /* 隐藏滚动条 */
                  &::-webkit-scrollbar {
                    width: 0;
                    display: none;
                  }
                  scrollbar-width: none; /* Firefox */
                  -ms-overflow-style: none; /* IE and Edge */
                }

                thead,
                tbody tr {
                  display: table;
                  width: 100%;
                  table-layout: fixed;
                }
              }
            }
          }
        }
      }
    }
  }
}

:deep(.el-dialog) {
  --el-dialog-bg-color: transparent;
  background-image: url('@/assets/images/tcbg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

/* 弹窗样式 */
.custom-dialog {
  --el-dialog-text-color: #fff;
  --el-dialog-border-radius: 8px;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

:deep(.el-dialog__headerbtn:hover .el-dialog__close) {
  color: #00a2ec;
}

/* 应急物资弹窗样式 */
.supplies-dialog {
  pointer-events: auto;
  .el-overlay-dialog {
    pointer-events: auto;
    .el-dialog {
      pointer-events: auto;
    }
  }
}

.supplies-dialog {
  .el-dialog {
    background: none !important;
    background-image: url('@/assets/images/tcbg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    box-shadow: none !important;
    padding: 0;
    .el-dialog__header {
      height: 50px;
      background: transparent !important;
      border-radius: 0px 0px 0px 0px;
      padding-left: 30px;
      .el-dialog__title {
        line-height: 50px;
        color: #fff;
        font-size: 24px;
      }
      .el-dialog__close {
        color: #fff;
        font-size: 30px;
      }
    }
    .el-dialog__body {
      padding: 20px 30px 30px 30px;
      .el-form {
        .el-form-item {
          margin-bottom: 20px;
          .el-form-item__label {
            color: #fff;
            font-size: 24px;
          }
        }
      }
      .el-table {
        background: transparent;
        .el-table__header-wrapper {
          .el-table__header {
            th {
              background: #1b72df !important;
              color: #fff;
              font-size: 24px;
              border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }
          }
        }
        .el-table__body-wrapper {
          .el-table__body {
            tr {
              background: transparent;
              td {
                color: #fff;
                font-size: 24px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
              }
              &:hover {
                background-color: rgba(0, 162, 236, 0.1);
              }
            }
          }
        }
      }
      .el-pagination {
        .el-pagination__total,
        .el-pagination__jump {
          color: #fff;
        }
        .btn-prev,
        .btn-next,
        .el-pager li {
          background: rgba(0, 152, 250, 0.2);
          color: #fff;
          border: 1px solid rgba(0, 152, 250, 0.5);
          &:hover {
            background: rgba(0, 152, 250, 0.3);
          }
          &.active {
            background: #0098fa;
            color: #fff;
          }
        }
      }
    }
  }
}

/* 更多按钮样式 */
.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .more-btn {
    transition: all 0.3s ease;

    &:hover {
      color: #66b1ff !important;
      transform: translateY(-1px);
    }
  }
}
.new-add-border {
  background: linear-gradient(155deg, rgba(0, 102, 255, 0.1) 0%, rgba(0, 102, 255, 0.1) 30%, rgba(0, 155, 255, 0) 100%);
  border: 2px solid rgba(64, 228, 251, 0.3);
  position: relative;
  .leftbei {
    position: absolute;
    left: 0;
    width: 7px;
    height: 197px;
    top: 30%;
    background: url('@/assets/images/intellisense/leftbei.png') no-repeat center/100%;
  }
  .righttbei {
    position: absolute;
    right: 0;
    width: 7px;
    height: 197px;
    top: 30%;
    background: url('@/assets/images/intellisense/rightbeibg.png') no-repeat center/100%;
  }
  .bottombeibg {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 6px;
    // left: 30%;
    background: url('@/assets/images/intellisense/bottombeibg.png') no-repeat center/100%;
    // &.bottom-left {
    //   left: 21%;
    // }
  }
}
</style>
